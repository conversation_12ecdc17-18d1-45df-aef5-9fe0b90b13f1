#!/bin/bash

# NeuroTrade OS - Startup Script
# This script helps you get NeuroTrade OS up and running quickly

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    if ! command_exists docker; then
        print_error "Docker is not installed. Please install Docker first."
        echo "Visit: https://docs.docker.com/get-docker/"
        exit 1
    fi
    
    if ! command_exists docker-compose; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        echo "Visit: https://docs.docker.com/compose/install/"
        exit 1
    fi
    
    print_success "Prerequisites check passed!"
}

# Function to setup environment files
setup_environment() {
    print_status "Setting up environment files..."
    
    # Backend environment
    if [ ! -f "backend/.env" ]; then
        print_status "Creating backend/.env from example..."
        cp backend/.env.example backend/.env
        print_warning "Please review and update backend/.env with your settings"
    else
        print_status "backend/.env already exists"
    fi
    
    # Frontend environment
    if [ ! -f "frontend/.env" ]; then
        print_status "Creating frontend/.env from example..."
        cp frontend/.env.example frontend/.env
        print_warning "Please review and update frontend/.env with your settings"
    else
        print_status "frontend/.env already exists"
    fi
    
    print_success "Environment files setup complete!"
}

# Function to start services
start_services() {
    print_status "Starting NeuroTrade OS services..."
    
    # Pull latest images
    print_status "Pulling Docker images..."
    docker-compose pull
    
    # Build and start services
    print_status "Building and starting services..."
    docker-compose up -d --build
    
    print_success "Services started successfully!"
}

# Function to wait for services
wait_for_services() {
    print_status "Waiting for services to be ready..."
    
    # Wait for database
    print_status "Waiting for PostgreSQL..."
    until docker-compose exec -T postgres pg_isready -U neurotrade; do
        sleep 2
    done
    
    # Wait for Redis
    print_status "Waiting for Redis..."
    until docker-compose exec -T redis redis-cli ping; do
        sleep 2
    done
    
    # Wait for InfluxDB
    print_status "Waiting for InfluxDB..."
    until docker-compose exec -T influxdb influx ping; do
        sleep 2
    done
    
    # Wait for backend
    print_status "Waiting for backend API..."
    until curl -f http://localhost:8000/health >/dev/null 2>&1; do
        sleep 5
    done
    
    print_success "All services are ready!"
}

# Function to run database migrations
run_migrations() {
    print_status "Running database migrations..."
    docker-compose exec backend alembic upgrade head
    print_success "Database migrations completed!"
}

# Function to create initial users
create_initial_users() {
    print_status "Creating initial users..."
    docker-compose exec backend python scripts/create_admin.py
    print_success "Initial users created!"
}

# Function to show service status
show_status() {
    print_status "Service Status:"
    docker-compose ps
    
    echo ""
    print_status "Access URLs:"
    echo "  🌐 Frontend:        http://localhost:3000"
    echo "  🔧 Backend API:     http://localhost:8000"
    echo "  📚 API Docs:        http://localhost:8000/docs"
    echo "  📊 InfluxDB UI:     http://localhost:8086"
    echo "  🌸 Flower Monitor:  http://localhost:5555"
    
    echo ""
    print_status "Default Credentials:"
    echo "  👑 Admin:   <EMAIL> / admin_secure_password"
    echo "  💼 Trader:  <EMAIL> / trader_password"
    echo "  👁️  Viewer:  <EMAIL> / viewer_password"
}

# Function to show logs
show_logs() {
    print_status "Showing service logs (Ctrl+C to exit)..."
    docker-compose logs -f
}

# Function to stop services
stop_services() {
    print_status "Stopping NeuroTrade OS services..."
    docker-compose down
    print_success "Services stopped!"
}

# Function to clean up
cleanup() {
    print_status "Cleaning up NeuroTrade OS..."
    docker-compose down -v --remove-orphans
    docker system prune -f
    print_success "Cleanup completed!"
}

# Function to show help
show_help() {
    echo "NeuroTrade OS - Startup Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  start     Start all services (default)"
    echo "  stop      Stop all services"
    echo "  restart   Restart all services"
    echo "  status    Show service status"
    echo "  logs      Show service logs"
    echo "  clean     Clean up all containers and volumes"
    echo "  help      Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                # Start all services"
    echo "  $0 start          # Start all services"
    echo "  $0 stop           # Stop all services"
    echo "  $0 logs           # Show logs"
}

# Main function
main() {
    echo "🚀 NeuroTrade OS - Advanced AI-Driven Cryptocurrency Trading Platform"
    echo "=================================================================="
    echo ""
    
    case "${1:-start}" in
        start)
            check_prerequisites
            setup_environment
            start_services
            wait_for_services
            run_migrations
            create_initial_users
            show_status
            ;;
        stop)
            stop_services
            ;;
        restart)
            stop_services
            sleep 2
            main start
            ;;
        status)
            show_status
            ;;
        logs)
            show_logs
            ;;
        clean)
            cleanup
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            print_error "Unknown command: $1"
            show_help
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
