"""
Notification and alert models
"""
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, Float, Enum, JSON
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum
from ..core.database import Base


class NotificationType(enum.Enum):
    EMAIL = "email"
    SMS = "sms"
    DISCORD = "discord"
    TELEGRAM = "telegram"
    WEBHOOK = "webhook"
    IN_APP = "in_app"


class NotificationStatus(enum.Enum):
    PENDING = "pending"
    SENT = "sent"
    FAILED = "failed"
    DELIVERED = "delivered"


class AlertType(enum.Enum):
    PRICE_ALERT = "price_alert"
    RSI_ALERT = "rsi_alert"
    VOLUME_ALERT = "volume_alert"
    PNL_ALERT = "pnl_alert"
    DRAWDOWN_ALERT = "drawdown_alert"
    BOT_ERROR = "bot_error"
    BOT_STOPPED = "bot_stopped"
    TRADE_EXECUTED = "trade_executed"
    SYSTEM_ALERT = "system_alert"


class Notification(Base):
    __tablename__ = "notifications"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, index=True, nullable=False)
    
    # Notification Details
    title = Column(String(200), nullable=False)
    message = Column(Text, nullable=False)
    notification_type = Column(Enum(NotificationType), nullable=False)
    
    # Status
    status = Column(Enum(NotificationStatus), default=NotificationStatus.PENDING)
    
    # Delivery Information
    recipient = Column(String(255))  # email, phone, webhook URL, etc.
    
    # Metadata
    metadata = Column(JSON)  # Additional data for the notification
    
    # Read Status (for in-app notifications)
    is_read = Column(Boolean, default=False)
    read_at = Column(DateTime(timezone=True))
    
    # Delivery Tracking
    sent_at = Column(DateTime(timezone=True))
    delivered_at = Column(DateTime(timezone=True))
    error_message = Column(Text)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    user = relationship("User", back_populates="notifications")


class Alert(Base):
    __tablename__ = "alerts"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, index=True, nullable=False)
    
    # Alert Configuration
    name = Column(String(100), nullable=False)
    alert_type = Column(Enum(AlertType), nullable=False)
    
    # Target
    symbol = Column(String(20))  # For price/volume alerts
    bot_id = Column(Integer)  # For bot-specific alerts
    
    # Conditions (JSON)
    conditions = Column(JSON, nullable=False)  # {"price": {"above": 50000}, "rsi": {"below": 30}}
    
    # Notification Settings
    notification_types = Column(JSON)  # ["email", "sms", "discord"]
    
    # Status
    is_active = Column(Boolean, default=True)
    is_triggered = Column(Boolean, default=False)
    
    # Cooldown (to prevent spam)
    cooldown_minutes = Column(Integer, default=60)
    last_triggered = Column(DateTime(timezone=True))
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())


class WebhookEndpoint(Base):
    __tablename__ = "webhook_endpoints"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, index=True, nullable=False)
    
    # Webhook Configuration
    name = Column(String(100), nullable=False)
    url = Column(String(500), nullable=False)
    secret = Column(String(255))  # For webhook signature verification
    
    # Event Filters
    events = Column(JSON)  # ["trade_executed", "bot_stopped", etc.]
    
    # Status
    is_active = Column(Boolean, default=True)
    
    # Delivery Settings
    retry_count = Column(Integer, default=3)
    timeout_seconds = Column(Integer, default=30)
    
    # Statistics
    total_deliveries = Column(Integer, default=0)
    successful_deliveries = Column(Integer, default=0)
    failed_deliveries = Column(Integer, default=0)
    last_delivery_at = Column(DateTime(timezone=True))
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())


class WebhookDelivery(Base):
    __tablename__ = "webhook_deliveries"
    
    id = Column(Integer, primary_key=True, index=True)
    webhook_id = Column(Integer, index=True, nullable=False)
    
    # Delivery Information
    event_type = Column(String(50), nullable=False)
    payload = Column(JSON, nullable=False)
    
    # Response
    status_code = Column(Integer)
    response_body = Column(Text)
    response_headers = Column(JSON)
    
    # Timing
    delivery_duration_ms = Column(Integer)
    
    # Status
    success = Column(Boolean, default=False)
    error_message = Column(Text)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())


class NotificationTemplate(Base):
    __tablename__ = "notification_templates"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Template Information
    name = Column(String(100), unique=True, nullable=False)
    notification_type = Column(Enum(NotificationType), nullable=False)
    event_type = Column(String(50), nullable=False)
    
    # Template Content
    subject_template = Column(String(200))  # For email
    body_template = Column(Text, nullable=False)
    
    # Variables (JSON)
    variables = Column(JSON)  # Available template variables
    
    # Status
    is_active = Column(Boolean, default=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
