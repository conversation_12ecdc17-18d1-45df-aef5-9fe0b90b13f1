@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap');

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply bg-gray-50 dark:bg-dark-900 text-gray-900 dark:text-gray-100;
  }
  
  * {
    @apply border-gray-200 dark:border-dark-700;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply btn bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500;
  }
  
  .btn-success {
    @apply btn bg-success-600 text-white hover:bg-success-700 focus:ring-success-500;
  }
  
  .btn-danger {
    @apply btn bg-danger-600 text-white hover:bg-danger-700 focus:ring-danger-500;
  }
  
  .btn-outline {
    @apply btn border-gray-300 dark:border-dark-600 text-gray-700 dark:text-gray-300 bg-white dark:bg-dark-800 hover:bg-gray-50 dark:hover:bg-dark-700 focus:ring-primary-500;
  }
  
  .input {
    @apply block w-full px-3 py-2 border border-gray-300 dark:border-dark-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 bg-white dark:bg-dark-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm;
  }
  
  .card {
    @apply bg-white dark:bg-dark-800 shadow rounded-lg border border-gray-200 dark:border-dark-700;
  }
  
  .card-header {
    @apply px-6 py-4 border-b border-gray-200 dark:border-dark-700;
  }
  
  .card-body {
    @apply px-6 py-4;
  }
  
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-success {
    @apply badge bg-success-100 text-success-800 dark:bg-success-900 dark:text-success-200;
  }
  
  .badge-danger {
    @apply badge bg-danger-100 text-danger-800 dark:bg-danger-900 dark:text-danger-200;
  }
  
  .badge-warning {
    @apply badge bg-warning-100 text-warning-800 dark:bg-warning-900 dark:text-warning-200;
  }
  
  .badge-info {
    @apply badge bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200;
  }
  
  .badge-gray {
    @apply badge bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200;
  }
  
  .table {
    @apply min-w-full divide-y divide-gray-200 dark:divide-dark-700;
  }
  
  .table-header {
    @apply bg-gray-50 dark:bg-dark-800;
  }
  
  .table-header-cell {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider;
  }
  
  .table-body {
    @apply bg-white dark:bg-dark-900 divide-y divide-gray-200 dark:divide-dark-700;
  }
  
  .table-cell {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100;
  }
  
  .sidebar-nav-item {
    @apply flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200;
  }
  
  .sidebar-nav-item-active {
    @apply sidebar-nav-item bg-primary-100 dark:bg-primary-900 text-primary-900 dark:text-primary-100;
  }
  
  .sidebar-nav-item-inactive {
    @apply sidebar-nav-item text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-dark-700 hover:text-gray-900 dark:hover:text-gray-100;
  }
}

@layer utilities {
  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-primary-400 bg-clip-text text-transparent;
  }
  
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  .animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-dark-800;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-300 dark:bg-dark-600 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400 dark:bg-dark-500;
}

/* Loading spinner */
.spinner {
  @apply inline-block w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin;
}

/* Chart container */
.chart-container {
  position: relative;
  height: 400px;
  width: 100%;
}

/* Status indicators */
.status-dot {
  @apply inline-block w-2 h-2 rounded-full;
}

.status-dot-green {
  @apply status-dot bg-success-500;
}

.status-dot-red {
  @apply status-dot bg-danger-500;
}

.status-dot-yellow {
  @apply status-dot bg-warning-500;
}

.status-dot-gray {
  @apply status-dot bg-gray-400;
}

/* Transitions */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
