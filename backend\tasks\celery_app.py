"""
Celery application configuration
"""
from celery import Celery
from celery.schedules import crontab
from ..core.config import settings

# Create Celery app
celery_app = Celery(
    "neurotrade",
    broker=settings.REDIS_URL,
    backend=settings.REDIS_URL,
    include=[
        "backend.tasks.market_data_tasks",
        "backend.tasks.notification_tasks",
        "backend.tasks.maintenance_tasks"
    ]
)

# Configure Celery
celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    task_track_started=True,
    task_time_limit=30 * 60,  # 30 minutes
    task_soft_time_limit=25 * 60,  # 25 minutes
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
)

# Periodic tasks schedule
celery_app.conf.beat_schedule = {
    # Market data collection
    "collect-market-data": {
        "task": "backend.tasks.market_data_tasks.collect_market_data",
        "schedule": 60.0,  # Every minute
    },
    
    # Process pending notifications
    "process-notifications": {
        "task": "backend.tasks.notification_tasks.process_pending_notifications",
        "schedule": 30.0,  # Every 30 seconds
    },
    
    # Check alerts
    "check-alerts": {
        "task": "backend.tasks.notification_tasks.check_alerts",
        "schedule": 60.0,  # Every minute
    },
    
    # Database cleanup
    "cleanup-old-data": {
        "task": "backend.tasks.maintenance_tasks.cleanup_old_data",
        "schedule": crontab(hour=2, minute=0),  # Daily at 2 AM
    },
    
    # Backup database
    "backup-database": {
        "task": "backend.tasks.maintenance_tasks.backup_database",
        "schedule": crontab(hour=3, minute=0),  # Daily at 3 AM
    },
    
    # Update bot statistics
    "update-bot-stats": {
        "task": "backend.tasks.maintenance_tasks.update_bot_statistics",
        "schedule": 300.0,  # Every 5 minutes
    },
    
    # Health check
    "system-health-check": {
        "task": "backend.tasks.maintenance_tasks.system_health_check",
        "schedule": 600.0,  # Every 10 minutes
    },
}

celery_app.conf.timezone = "UTC"
