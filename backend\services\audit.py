"""
Audit logging service
"""
import json
from datetime import datetime
from typing import Optional, Dict, Any
from sqlalchemy.orm import Session
from ..core.database import SessionLocal
from ..models.user import AuditLog


class AuditService:
    """Service for audit logging"""
    
    @staticmethod
    async def log_action(
        action: str,
        user_id: Optional[int] = None,
        resource: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        session_id: Optional[str] = None,
        success: bool = True,
        error_message: Optional[str] = None
    ):
        """Log an audit action"""
        db = SessionLocal()
        try:
            audit_log = AuditLog(
                user_id=user_id,
                action=action,
                resource=resource,
                details=json.dumps(details) if details else None,
                ip_address=ip_address,
                user_agent=user_agent,
                session_id=session_id,
                success=success,
                error_message=error_message
            )
            
            db.add(audit_log)
            db.commit()
        except Exception as e:
            db.rollback()
            # Log to file or external service if database logging fails
            print(f"Failed to log audit action: {e}")
        finally:
            db.close()
    
    @staticmethod
    def get_user_audit_logs(
        user_id: int,
        limit: int = 100,
        offset: int = 0,
        db: Session = None
    ):
        """Get audit logs for a user"""
        if db is None:
            db = SessionLocal()
            close_db = True
        else:
            close_db = False
        
        try:
            logs = db.query(AuditLog).filter(
                AuditLog.user_id == user_id
            ).order_by(
                AuditLog.created_at.desc()
            ).offset(offset).limit(limit).all()
            
            return logs
        finally:
            if close_db:
                db.close()
    
    @staticmethod
    def get_system_audit_logs(
        action: Optional[str] = None,
        success: Optional[bool] = None,
        limit: int = 100,
        offset: int = 0,
        db: Session = None
    ):
        """Get system audit logs"""
        if db is None:
            db = SessionLocal()
            close_db = True
        else:
            close_db = False
        
        try:
            query = db.query(AuditLog)
            
            if action:
                query = query.filter(AuditLog.action == action)
            
            if success is not None:
                query = query.filter(AuditLog.success == success)
            
            logs = query.order_by(
                AuditLog.created_at.desc()
            ).offset(offset).limit(limit).all()
            
            return logs
        finally:
            if close_db:
                db.close()
