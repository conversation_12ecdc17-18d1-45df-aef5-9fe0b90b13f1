# NeuroTrade OS Frontend Environment Configuration

# API Configuration
REACT_APP_API_URL=http://localhost:8000/api/v1
REACT_APP_WS_URL=ws://localhost:8000

# Application Configuration
REACT_APP_NAME=NeuroTrade OS
REACT_APP_VERSION=1.0.0
REACT_APP_DESCRIPTION=Advanced AI-Driven Cryptocurrency Trading Platform

# Feature Flags
REACT_APP_ENABLE_DARK_MODE=true
REACT_APP_ENABLE_NOTIFICATIONS=true
REACT_APP_ENABLE_CHARTS=true
REACT_APP_ENABLE_EXPORT=true

# Development Configuration
CHOKIDAR_USEPOLLING=true
FAST_REFRESH=true

# Build Configuration
GENERATE_SOURCEMAP=false
INLINE_RUNTIME_CHUNK=false

# Analytics (Optional)
REACT_APP_GOOGLE_ANALYTICS_ID=your_ga_id_here
REACT_APP_MIXPANEL_TOKEN=your_mixpanel_token_here

# Error Tracking (Optional)
REACT_APP_SENTRY_DSN=your_sentry_dsn_here

# Social Links
REACT_APP_GITHUB_URL=https://github.com/your-username/neurotrade-os
REACT_APP_DISCORD_URL=https://discord.gg/your-discord
REACT_APP_TWITTER_URL=https://twitter.com/neurotrade
