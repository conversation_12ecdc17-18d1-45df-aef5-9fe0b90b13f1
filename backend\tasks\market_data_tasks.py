"""
Market data collection tasks
"""
import logging
from datetime import datetime
from typing import Dict, Any
from celery import current_task
from .celery_app import celery_app
from ..core.database import SessionLocal
from ..services.market_data import MarketDataService

logger = logging.getLogger(__name__)


@celery_app.task(bind=True)
def collect_market_data(self):
    """Collect market data from exchanges"""
    try:
        # This would be handled by the MarketDataService
        # which runs continuously in the main application
        logger.info("Market data collection task executed")
        return {"status": "success", "timestamp": datetime.utcnow().isoformat()}
    
    except Exception as e:
        logger.error(f"Error in market data collection: {e}")
        raise self.retry(exc=e, countdown=60, max_retries=3)


@celery_app.task(bind=True)
def update_price_alerts(self):
    """Check and trigger price alerts"""
    try:
        from ..models.notifications import Alert, AlertType
        from ..models.trading import TradingBot
        
        db = SessionLocal()
        try:
            # Get active price alerts
            price_alerts = db.query(Alert).filter(
                Alert.alert_type == AlertType.PRICE_ALERT,
                Alert.is_active == True,
                Alert.is_triggered == False
            ).all()
            
            triggered_count = 0
            
            for alert in price_alerts:
                # Check if alert should be triggered
                # This is a simplified version - would need actual price checking
                conditions = alert.conditions
                symbol = alert.symbol
                
                if symbol and "price" in conditions:
                    # Get current price (simplified)
                    current_price = 50000  # Would get from market data service
                    
                    target_price = conditions["price"].get("above") or conditions["price"].get("below")
                    
                    should_trigger = False
                    if "above" in conditions["price"] and current_price > conditions["price"]["above"]:
                        should_trigger = True
                    elif "below" in conditions["price"] and current_price < conditions["price"]["below"]:
                        should_trigger = True
                    
                    if should_trigger:
                        # Trigger alert
                        alert.is_triggered = True
                        alert.last_triggered = datetime.utcnow()
                        triggered_count += 1
                        
                        # Send notification (would be handled by notification service)
                        send_alert_notification.delay(alert.id, current_price)
            
            db.commit()
            
            return {
                "status": "success",
                "alerts_checked": len(price_alerts),
                "alerts_triggered": triggered_count
            }
            
        finally:
            db.close()
    
    except Exception as e:
        logger.error(f"Error updating price alerts: {e}")
        raise self.retry(exc=e, countdown=60, max_retries=3)


@celery_app.task(bind=True)
def send_alert_notification(self, alert_id: int, current_price: float):
    """Send notification for triggered alert"""
    try:
        from ..models.notifications import Alert, Notification, NotificationType, NotificationStatus
        
        db = SessionLocal()
        try:
            alert = db.query(Alert).filter(Alert.id == alert_id).first()
            if not alert:
                return {"status": "error", "message": "Alert not found"}
            
            # Create notification
            notification = Notification(
                user_id=alert.user_id,
                title=f"Price Alert: {alert.name}",
                message=f"Price alert triggered for {alert.symbol} at ${current_price}",
                notification_type=NotificationType.IN_APP,
                status=NotificationStatus.SENT
            )
            
            db.add(notification)
            db.commit()
            
            return {"status": "success", "notification_id": notification.id}
            
        finally:
            db.close()
    
    except Exception as e:
        logger.error(f"Error sending alert notification: {e}")
        raise self.retry(exc=e, countdown=30, max_retries=3)


@celery_app.task(bind=True)
def calculate_technical_indicators(self, symbol: str, exchange: str):
    """Calculate technical indicators for a symbol"""
    try:
        # This would calculate various technical indicators
        # and store them for use by trading strategies
        
        logger.info(f"Calculating technical indicators for {symbol} on {exchange}")
        
        # Placeholder for actual calculation
        indicators = {
            "rsi": 45.5,
            "macd": 0.25,
            "bollinger_upper": 52000,
            "bollinger_lower": 48000,
            "sma_20": 50000,
            "ema_12": 50100
        }
        
        # Store in Redis or database for quick access
        from ..core.database import get_redis
        redis_client = get_redis()
        
        key = f"indicators:{exchange}:{symbol}"
        redis_client.hset(key, mapping=indicators)
        redis_client.expire(key, 300)  # Expire after 5 minutes
        
        return {
            "status": "success",
            "symbol": symbol,
            "exchange": exchange,
            "indicators": indicators
        }
    
    except Exception as e:
        logger.error(f"Error calculating technical indicators: {e}")
        raise self.retry(exc=e, countdown=60, max_retries=3)


@celery_app.task(bind=True)
def update_portfolio_values(self):
    """Update portfolio values for all users"""
    try:
        from ..models.trading import Portfolio
        
        db = SessionLocal()
        try:
            portfolios = db.query(Portfolio).all()
            updated_count = 0
            
            for portfolio in portfolios:
                # Calculate current portfolio value
                # This would involve getting current prices for all holdings
                
                # Simplified calculation
                total_value = 0
                if portfolio.balances:
                    for currency, amount in portfolio.balances.items():
                        if currency == "USDT" or currency == "USD":
                            total_value += float(amount)
                        else:
                            # Would get current price and multiply
                            price = 50000 if currency == "BTC" else 3000  # Simplified
                            total_value += float(amount) * price
                
                portfolio.total_value_usd = total_value
                
                # Calculate P&L
                if portfolio.initial_value:
                    portfolio.profit_loss = total_value - portfolio.initial_value
                    portfolio.profit_loss_percentage = (portfolio.profit_loss / portfolio.initial_value) * 100
                
                updated_count += 1
            
            db.commit()
            
            return {
                "status": "success",
                "portfolios_updated": updated_count
            }
            
        finally:
            db.close()
    
    except Exception as e:
        logger.error(f"Error updating portfolio values: {e}")
        raise self.retry(exc=e, countdown=120, max_retries=3)


@celery_app.task(bind=True)
def sync_exchange_balances(self, user_id: int, exchange: str):
    """Sync user's exchange balances"""
    try:
        from ..models.user import ExchangeAPIKey
        from ..models.trading import Portfolio
        from ..core.security import APIKeyManager
        
        db = SessionLocal()
        try:
            # Get user's API key for the exchange
            api_key_record = db.query(ExchangeAPIKey).filter(
                ExchangeAPIKey.user_id == user_id,
                ExchangeAPIKey.exchange_name == exchange,
                ExchangeAPIKey.is_active == True
            ).first()
            
            if not api_key_record:
                return {"status": "error", "message": "No API key found"}
            
            # Decrypt API keys
            api_key = APIKeyManager.decrypt_key(api_key_record.api_key)
            secret_key = APIKeyManager.decrypt_key(api_key_record.secret_key)
            
            # Initialize exchange client
            import ccxt
            
            exchange_config = {
                'apiKey': api_key,
                'secret': secret_key,
                'sandbox': api_key_record.is_testnet,
                'enableRateLimit': True,
            }
            
            if exchange.lower() == 'binance':
                exchange_client = ccxt.binance(exchange_config)
            elif exchange.lower() == 'coinbase':
                exchange_client = ccxt.coinbasepro(exchange_config)
            else:
                return {"status": "error", "message": f"Unsupported exchange: {exchange}"}
            
            # Fetch balance
            balance = exchange_client.fetch_balance()
            
            # Update or create portfolio record
            portfolio = db.query(Portfolio).filter(
                Portfolio.user_id == user_id,
                Portfolio.exchange == exchange
            ).first()
            
            if not portfolio:
                portfolio = Portfolio(
                    user_id=user_id,
                    exchange=exchange,
                    initial_value=0
                )
                db.add(portfolio)
            
            # Update balances
            balances = {}
            for currency, amounts in balance.items():
                if currency != 'info' and amounts['total'] > 0:
                    balances[currency] = amounts['total']
            
            portfolio.balances = balances
            
            db.commit()
            
            return {
                "status": "success",
                "user_id": user_id,
                "exchange": exchange,
                "balances": balances
            }
            
        finally:
            db.close()
    
    except Exception as e:
        logger.error(f"Error syncing exchange balances: {e}")
        raise self.retry(exc=e, countdown=120, max_retries=3)
