"""
Trading-related models
"""
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, Float, Enum, JSON
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum
from ..core.database import Base


class BotStatus(enum.Enum):
    STOPPED = "stopped"
    RUNNING = "running"
    PAUSED = "paused"
    ERROR = "error"


class BotStrategy(enum.Enum):
    RSI = "rsi"
    MACD = "macd"
    MA_CROSSOVER = "ma_crossover"
    BOLLINGER_BANDS = "bollinger_bands"
    VWAP = "vwap"
    GRID = "grid"
    MEAN_REVERSION = "mean_reversion"
    BREAKOUT = "breakout"
    FIBONACCI = "fibonacci"
    CUSTOM = "custom"


class TradeType(enum.Enum):
    BUY = "buy"
    SELL = "sell"


class TradeStatus(enum.Enum):
    PENDING = "pending"
    FILLED = "filled"
    PARTIALLY_FILLED = "partially_filled"
    CANCELLED = "cancelled"
    FAILED = "failed"


class OrderType(enum.Enum):
    MARKET = "market"
    LIMIT = "limit"
    STOP_LOSS = "stop_loss"
    TAKE_PROFIT = "take_profit"


class TradingBot(Base):
    __tablename__ = "trading_bots"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, index=True, nullable=False)
    
    # Bot Configuration
    name = Column(String(100), nullable=False)
    description = Column(Text)
    strategy = Column(Enum(BotStrategy), nullable=False)
    
    # Trading Pair
    symbol = Column(String(20), nullable=False)  # BTC/USDT, ETH/BTC, etc.
    exchange = Column(String(50), nullable=False)  # binance, coinbase, etc.
    
    # Status
    status = Column(Enum(BotStatus), default=BotStatus.STOPPED)
    is_testnet = Column(Boolean, default=False)
    
    # Strategy Parameters (JSON)
    strategy_config = Column(JSON)  # RSI levels, MA periods, etc.
    
    # Risk Management
    position_size = Column(Float, default=100.0)  # USD amount per trade
    risk_percentage = Column(Float, default=2.0)  # % of portfolio at risk
    max_drawdown = Column(Float, default=10.0)  # Maximum allowed drawdown %
    stop_loss_percentage = Column(Float, default=2.0)
    take_profit_percentage = Column(Float, default=4.0)
    
    # Performance Tracking
    total_trades = Column(Integer, default=0)
    winning_trades = Column(Integer, default=0)
    losing_trades = Column(Integer, default=0)
    total_profit_loss = Column(Float, default=0.0)
    current_drawdown = Column(Float, default=0.0)
    max_drawdown_reached = Column(Float, default=0.0)
    
    # Execution Settings
    execution_interval = Column(Integer, default=60)  # seconds
    last_execution = Column(DateTime(timezone=True))
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    started_at = Column(DateTime(timezone=True))
    stopped_at = Column(DateTime(timezone=True))
    
    # Relationships
    owner = relationship("User", back_populates="bots")
    trades = relationship("Trade", back_populates="bot")
    signals = relationship("TradingSignal", back_populates="bot")


class Trade(Base):
    __tablename__ = "trades"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, index=True, nullable=False)
    bot_id = Column(Integer, index=True)
    
    # Trade Information
    symbol = Column(String(20), nullable=False)
    exchange = Column(String(50), nullable=False)
    trade_type = Column(Enum(TradeType), nullable=False)
    order_type = Column(Enum(OrderType), nullable=False)
    
    # Order Details
    quantity = Column(Float, nullable=False)
    price = Column(Float)  # For limit orders
    filled_price = Column(Float)  # Actual execution price
    filled_quantity = Column(Float, default=0.0)
    
    # Status
    status = Column(Enum(TradeStatus), default=TradeStatus.PENDING)
    exchange_order_id = Column(String(100))  # Exchange's order ID
    
    # Financial
    total_value = Column(Float)  # Total trade value
    fees = Column(Float, default=0.0)
    profit_loss = Column(Float, default=0.0)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    executed_at = Column(DateTime(timezone=True))
    
    # Relationships
    user = relationship("User", back_populates="trades")
    bot = relationship("TradingBot", back_populates="trades")


class TradingSignal(Base):
    __tablename__ = "trading_signals"
    
    id = Column(Integer, primary_key=True, index=True)
    bot_id = Column(Integer, index=True, nullable=False)
    
    # Signal Information
    symbol = Column(String(20), nullable=False)
    signal_type = Column(Enum(TradeType), nullable=False)  # BUY or SELL
    strength = Column(Float)  # Signal strength 0-100
    confidence = Column(Float)  # Confidence level 0-100
    
    # Market Data at Signal Time
    price = Column(Float, nullable=False)
    volume = Column(Float)
    
    # Indicators (JSON)
    indicators = Column(JSON)  # RSI, MACD, etc. values
    
    # Action Taken
    action_taken = Column(Boolean, default=False)
    trade_id = Column(Integer)  # If trade was executed
    
    # Reasoning
    reason = Column(Text)  # Why this signal was generated
    
    # Timestamp
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    bot = relationship("TradingBot", back_populates="signals")


class Portfolio(Base):
    __tablename__ = "portfolios"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, index=True, nullable=False)
    exchange = Column(String(50), nullable=False)
    
    # Portfolio Data (JSON)
    balances = Column(JSON)  # {"BTC": 0.5, "USDT": 1000.0, ...}
    total_value_usd = Column(Float, default=0.0)
    
    # Performance
    initial_value = Column(Float)
    profit_loss = Column(Float, default=0.0)
    profit_loss_percentage = Column(Float, default=0.0)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())


class Backtest(Base):
    __tablename__ = "backtests"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, index=True, nullable=False)
    bot_id = Column(Integer, index=True)
    
    # Backtest Configuration
    name = Column(String(100), nullable=False)
    symbol = Column(String(20), nullable=False)
    strategy = Column(Enum(BotStrategy), nullable=False)
    strategy_config = Column(JSON)
    
    # Time Range
    start_date = Column(DateTime(timezone=True), nullable=False)
    end_date = Column(DateTime(timezone=True), nullable=False)
    
    # Initial Conditions
    initial_balance = Column(Float, default=10000.0)
    
    # Results
    final_balance = Column(Float)
    total_return = Column(Float)
    total_return_percentage = Column(Float)
    max_drawdown = Column(Float)
    sharpe_ratio = Column(Float)
    total_trades = Column(Integer)
    winning_trades = Column(Integer)
    win_rate = Column(Float)
    
    # Detailed Results (JSON)
    trade_history = Column(JSON)
    equity_curve = Column(JSON)
    
    # Status
    is_completed = Column(Boolean, default=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    completed_at = Column(DateTime(timezone=True))
