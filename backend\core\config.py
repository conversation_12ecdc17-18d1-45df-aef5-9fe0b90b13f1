"""
Configuration management for NeuroTrade OS
"""
import os
from typing import List, Optional
from pydantic_settings import BaseSettings
from pydantic import validator


class Settings(BaseSettings):
    # Database
    DATABASE_URL: str = "postgresql://neurotrade:neurotrade_secure_password@localhost:5432/neurotrade"
    REDIS_URL: str = "redis://localhost:6379"
    
    # InfluxDB
    INFLUXDB_URL: str = "http://localhost:8086"
    INFLUXDB_TOKEN: str = "neurotrade_admin_token"
    INFLUXDB_ORG: str = "neurotrade"
    INFLUXDB_BUCKET: str = "market_data"
    
    # Security
    SECRET_KEY: str = "your_super_secret_key_change_in_production_minimum_32_characters"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    
    # Environment
    ENVIRONMENT: str = "development"
    DEBUG: bool = True
    
    # CORS
    CORS_ORIGINS: List[str] = ["http://localhost:3000", "http://127.0.0.1:3000"]
    
    # Email Configuration
    SENDGRID_API_KEY: Optional[str] = None
    FROM_EMAIL: str = "<EMAIL>"
    
    # SMS Configuration
    TWILIO_ACCOUNT_SID: Optional[str] = None
    TWILIO_AUTH_TOKEN: Optional[str] = None
    TWILIO_PHONE_NUMBER: Optional[str] = None
    
    # Discord
    DISCORD_WEBHOOK_URL: Optional[str] = None
    
    # Admin
    ADMIN_EMAIL: str = "<EMAIL>"
    ADMIN_PASSWORD: str = "admin_secure_password"
    
    # Rate Limiting
    RATE_LIMIT_PER_MINUTE: int = 60
    
    # Logging
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "logs/neurotrade.log"
    
    # Backup
    BACKUP_ENABLED: bool = True
    BACKUP_INTERVAL_HOURS: int = 24
    S3_BUCKET_NAME: Optional[str] = None
    AWS_ACCESS_KEY_ID: Optional[str] = None
    AWS_SECRET_ACCESS_KEY: Optional[str] = None
    AWS_REGION: str = "us-east-1"
    
    # License
    LICENSE_SERVER_URL: Optional[str] = None
    LICENSE_CHECK_INTERVAL_HOURS: int = 24
    
    # WebSocket
    WS_MAX_CONNECTIONS: int = 1000
    WS_HEARTBEAT_INTERVAL: int = 30
    
    # Trading
    MAX_CONCURRENT_TRADES: int = 10
    DEFAULT_RISK_PERCENTAGE: float = 2.0
    MAX_RISK_PERCENTAGE: float = 10.0
    
    # Bot Configuration
    MAX_BOTS_PER_USER: int = 5
    BOT_EXECUTION_INTERVAL_SECONDS: int = 5
    BACKTEST_MAX_DAYS: int = 365
    
    @validator('CORS_ORIGINS', pre=True)
    def assemble_cors_origins(cls, v):
        if isinstance(v, str):
            return [i.strip() for i in v.split(",")]
        return v
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get application settings"""
    return settings
