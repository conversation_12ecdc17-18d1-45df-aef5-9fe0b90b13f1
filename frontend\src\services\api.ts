import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import { store } from '../store/store';
import { refreshToken, logout } from '../store/slices/authSlice';

// Create axios instance
const api: AxiosInstance = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:8000/api/v1',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle token refresh
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        await store.dispatch(refreshToken());
        const newToken = localStorage.getItem('token');
        if (newToken) {
          originalRequest.headers.Authorization = `Bearer ${newToken}`;
          return api(originalRequest);
        }
      } catch (refreshError) {
        store.dispatch(logout());
        window.location.href = '/login';
        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: async (credentials: { email: string; password: string; totp_code?: string }) => {
    const response = await api.post('/auth/login', credentials);
    return response.data;
  },

  register: async (userData: {
    email: string;
    username: string;
    password: string;
    first_name?: string;
    last_name?: string;
  }) => {
    const response = await api.post('/auth/register', userData);
    return response.data;
  },

  logout: async () => {
    const response = await api.post('/auth/logout');
    return response.data;
  },

  getCurrentUser: async () => {
    const response = await api.get('/auth/me');
    return response.data;
  },

  refreshToken: async (refreshToken: string) => {
    const response = await api.post('/auth/refresh', { refresh_token: refreshToken });
    return response.data;
  },

  setup2FA: async () => {
    const response = await api.post('/auth/setup-2fa');
    return response.data;
  },

  verify2FA: async (secret: string, totp_code: string) => {
    const response = await api.post('/auth/verify-2fa', { secret, totp_code });
    return response.data;
  },

  disable2FA: async (totp_code: string) => {
    const response = await api.post('/auth/disable-2fa', { totp_code });
    return response.data;
  },

  changePassword: async (current_password: string, new_password: string) => {
    const response = await api.post('/auth/change-password', {
      current_password,
      new_password,
    });
    return response.data;
  },
};

// Bots API
export const botsAPI = {
  getBots: async () => {
    const response = await api.get('/bots');
    return response.data;
  },

  getBot: async (id: number) => {
    const response = await api.get(`/bots/${id}`);
    return response.data;
  },

  createBot: async (botData: any) => {
    const response = await api.post('/bots', botData);
    return response.data;
  },

  updateBot: async (id: number, botData: any) => {
    const response = await api.put(`/bots/${id}`, botData);
    return response.data;
  },

  deleteBot: async (id: number) => {
    const response = await api.delete(`/bots/${id}`);
    return response.data;
  },

  startBot: async (id: number) => {
    const response = await api.post(`/bots/${id}/start`);
    return response.data;
  },

  stopBot: async (id: number) => {
    const response = await api.post(`/bots/${id}/stop`);
    return response.data;
  },

  pauseBot: async (id: number) => {
    const response = await api.post(`/bots/${id}/pause`);
    return response.data;
  },

  resumeBot: async (id: number) => {
    const response = await api.post(`/bots/${id}/resume`);
    return response.data;
  },

  getBotStatus: async (id: number) => {
    const response = await api.get(`/bots/${id}/status`);
    return response.data;
  },

  getBotSignals: async (id: number, limit: number = 50) => {
    const response = await api.get(`/bots/${id}/signals?limit=${limit}`);
    return response.data;
  },

  getAvailableStrategies: async () => {
    const response = await api.get('/bots/strategies/available');
    return response.data;
  },
};

// Trades API
export const tradesAPI = {
  getTrades: async (params?: any) => {
    const response = await api.get('/trades', { params });
    return response.data;
  },

  getTrade: async (id: number) => {
    const response = await api.get(`/trades/${id}`);
    return response.data;
  },

  getTradeStats: async (params?: any) => {
    const response = await api.get('/trades/stats/summary', { params });
    return response.data;
  },

  getDailyStats: async (days: number = 30) => {
    const response = await api.get(`/trades/stats/daily?days=${days}`);
    return response.data;
  },

  getStatsBySymbol: async (days: number = 30) => {
    const response = await api.get(`/trades/stats/by-symbol?days=${days}`);
    return response.data;
  },

  getStatsByBot: async (days: number = 30) => {
    const response = await api.get(`/trades/stats/by-bot?days=${days}`);
    return response.data;
  },

  exportTrades: async (days: number = 30) => {
    const response = await api.get(`/trades/export/csv?days=${days}`);
    return response.data;
  },
};

// Exchanges API
export const exchangesAPI = {
  getSupportedExchanges: async () => {
    const response = await api.get('/exchanges/supported');
    return response.data;
  },

  getApiKeys: async () => {
    const response = await api.get('/exchanges/api-keys');
    return response.data;
  },

  addApiKey: async (keyData: any) => {
    const response = await api.post('/exchanges/api-keys', keyData);
    return response.data;
  },

  updateApiKey: async (id: number, updates: any) => {
    const response = await api.put(`/exchanges/api-keys/${id}`, updates);
    return response.data;
  },

  deleteApiKey: async (id: number) => {
    const response = await api.delete(`/exchanges/api-keys/${id}`);
    return response.data;
  },

  testApiKey: async (id: number) => {
    const response = await api.post(`/exchanges/api-keys/${id}/test`);
    return response.data;
  },

  getMarkets: async (exchange: string) => {
    const response = await api.get(`/exchanges/markets/${exchange}`);
    return response.data;
  },
};

// Users API
export const usersAPI = {
  getProfile: async () => {
    const response = await api.get('/users/profile');
    return response.data;
  },

  updateProfile: async (profileData: any) => {
    const response = await api.put('/users/profile', profileData);
    return response.data;
  },

  changeEmail: async (new_email: string, password: string) => {
    const response = await api.post('/users/change-email', { new_email, password });
    return response.data;
  },

  getSettings: async () => {
    const response = await api.get('/users/settings');
    return response.data;
  },

  getActivity: async () => {
    const response = await api.get('/users/activity');
    return response.data;
  },

  getDashboardStats: async () => {
    const response = await api.get('/users/dashboard-stats');
    return response.data;
  },

  deleteAccount: async (password: string) => {
    const response = await api.delete('/users/account', { data: { password } });
    return response.data;
  },
};

// Notifications API
export const notificationsAPI = {
  getNotifications: async (params?: any) => {
    const response = await api.get('/notifications', { params });
    return response.data;
  },

  markAsRead: async (id: number) => {
    const response = await api.post(`/notifications/${id}/mark-read`);
    return response.data;
  },

  markAllAsRead: async () => {
    const response = await api.post('/notifications/mark-all-read');
    return response.data;
  },

  deleteNotification: async (id: number) => {
    const response = await api.delete(`/notifications/${id}`);
    return response.data;
  },

  getAlerts: async () => {
    const response = await api.get('/notifications/alerts');
    return response.data;
  },

  createAlert: async (alertData: any) => {
    const response = await api.post('/notifications/alerts', alertData);
    return response.data;
  },

  updateAlert: async (id: number, updates: any) => {
    const response = await api.put(`/notifications/alerts/${id}`, updates);
    return response.data;
  },

  deleteAlert: async (id: number) => {
    const response = await api.delete(`/notifications/alerts/${id}`);
    return response.data;
  },

  getWebhooks: async () => {
    const response = await api.get('/notifications/webhooks');
    return response.data;
  },

  createWebhook: async (webhookData: any) => {
    const response = await api.post('/notifications/webhooks', webhookData);
    return response.data;
  },

  updateWebhook: async (id: number, updates: any) => {
    const response = await api.put(`/notifications/webhooks/${id}`, updates);
    return response.data;
  },

  deleteWebhook: async (id: number) => {
    const response = await api.delete(`/notifications/webhooks/${id}`);
    return response.data;
  },

  testWebhook: async (id: number) => {
    const response = await api.post(`/notifications/webhooks/${id}/test`);
    return response.data;
  },

  getNotificationSettings: async () => {
    const response = await api.get('/notifications/settings');
    return response.data;
  },
};

// Admin API
export const adminAPI = {
  getStats: async () => {
    const response = await api.get('/admin/stats');
    return response.data;
  },

  getUsers: async (params?: any) => {
    const response = await api.get('/admin/users', { params });
    return response.data;
  },

  getUser: async (id: number) => {
    const response = await api.get(`/admin/users/${id}`);
    return response.data;
  },

  updateUser: async (id: number, updates: any) => {
    const response = await api.put(`/admin/users/${id}`, updates);
    return response.data;
  },

  resetUserHwid: async (id: number) => {
    const response = await api.post(`/admin/users/${id}/reset-hwid`);
    return response.data;
  },

  disableUser2FA: async (id: number) => {
    const response = await api.post(`/admin/users/${id}/disable-2fa`);
    return response.data;
  },

  getAllBots: async (params?: any) => {
    const response = await api.get('/admin/bots', { params });
    return response.data;
  },

  stopBot: async (id: number) => {
    const response = await api.post(`/admin/bots/${id}/stop`);
    return response.data;
  },

  getAuditLogs: async (params?: any) => {
    const response = await api.get('/admin/audit-logs', { params });
    return response.data;
  },

  getSystemHealth: async () => {
    const response = await api.get('/admin/system-health');
    return response.data;
  },

  broadcastMessage: async (title: string, message: string) => {
    const response = await api.post('/admin/broadcast', { title, message });
    return response.data;
  },
};

export default api;
