version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: neurotrade
      POSTGRES_USER: neurotrade
      POSTGRES_PASSWORD: neurotrade_secure_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    restart: unless-stopped

  # Redis Cache & Session Store
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  # InfluxDB for Time Series Data
  influxdb:
    image: influxdb:2.7
    environment:
      DOCKER_INFLUXDB_INIT_MODE: setup
      DOCKER_INFLUXDB_INIT_USERNAME: neurotrade
      DOCKER_INFLUXDB_INIT_PASSWORD: neurotrade_influx_password
      DOCKER_INFLUXDB_INIT_ORG: neurotrade
      DOCKER_INFLUXDB_INIT_BUCKET: market_data
      DOCKER_INFLUXDB_INIT_ADMIN_TOKEN: neurotrade_admin_token
    volumes:
      - influxdb_data:/var/lib/influxdb2
    ports:
      - "8086:8086"
    restart: unless-stopped

  # Backend API
  backend:
    build:
      context: .
      dockerfile: backend/Dockerfile
    environment:
      - DATABASE_URL=****************************************************************/neurotrade
      - REDIS_URL=redis://redis:6379
      - INFLUXDB_URL=http://influxdb:8086
      - INFLUXDB_TOKEN=neurotrade_admin_token
      - INFLUXDB_ORG=neurotrade
      - INFLUXDB_BUCKET=market_data
      - SECRET_KEY=your_super_secret_key_change_in_production
      - ENVIRONMENT=production
    ports:
      - "8000:8000"
    depends_on:
      - postgres
      - redis
      - influxdb
    volumes:
      - ./backend:/app
      - ./logs:/app/logs
    restart: unless-stopped

  # Celery Worker
  celery_worker:
    build:
      context: .
      dockerfile: backend/Dockerfile
    command: celery -A backend.tasks.celery_app worker --loglevel=info
    environment:
      - DATABASE_URL=****************************************************************/neurotrade
      - REDIS_URL=redis://redis:6379
      - INFLUXDB_URL=http://influxdb:8086
      - INFLUXDB_TOKEN=neurotrade_admin_token
      - INFLUXDB_ORG=neurotrade
      - INFLUXDB_BUCKET=market_data
      - SECRET_KEY=your_super_secret_key_change_in_production
    depends_on:
      - postgres
      - redis
      - influxdb
    volumes:
      - ./backend:/app
      - ./logs:/app/logs
    restart: unless-stopped

  # Celery Beat Scheduler
  celery_beat:
    build:
      context: .
      dockerfile: backend/Dockerfile
    command: celery -A backend.tasks.celery_app beat --loglevel=info
    environment:
      - DATABASE_URL=****************************************************************/neurotrade
      - REDIS_URL=redis://redis:6379
      - INFLUXDB_URL=http://influxdb:8086
      - INFLUXDB_TOKEN=neurotrade_admin_token
      - INFLUXDB_ORG=neurotrade
      - INFLUXDB_BUCKET=market_data
      - SECRET_KEY=your_super_secret_key_change_in_production
    depends_on:
      - postgres
      - redis
      - influxdb
    volumes:
      - ./backend:/app
      - ./logs:/app/logs
    restart: unless-stopped

  # Flower (Celery Monitoring)
  flower:
    build:
      context: .
      dockerfile: backend/Dockerfile
    command: celery -A backend.tasks.celery_app flower --port=5555
    environment:
      - DATABASE_URL=****************************************************************/neurotrade
      - REDIS_URL=redis://redis:6379
    ports:
      - "5555:5555"
    depends_on:
      - redis
    restart: unless-stopped

  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8000
      - REACT_APP_WS_URL=ws://localhost:8000
    volumes:
      - ./frontend:/app
      - /app/node_modules
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  influxdb_data:
