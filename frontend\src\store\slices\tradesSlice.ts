import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { tradesAPI } from '../../services/api';

export interface Trade {
  id: number;
  bot_id?: number;
  bot_name?: string;
  symbol: string;
  exchange: string;
  trade_type: string;
  order_type: string;
  quantity: number;
  price?: number;
  filled_price?: number;
  filled_quantity: number;
  total_value?: number;
  fees: number;
  profit_loss: number;
  status: string;
  exchange_order_id?: string;
  created_at: string;
  executed_at?: string;
}

export interface TradeStats {
  total_trades: number;
  winning_trades: number;
  losing_trades: number;
  total_profit_loss: number;
  win_rate: number;
  average_profit: number;
  average_loss: number;
  largest_win: number;
  largest_loss: number;
  total_fees: number;
}

interface TradesState {
  trades: Trade[];
  stats: TradeStats | null;
  dailyStats: any[];
  symbolStats: any[];
  botStats: any[];
  isLoading: boolean;
  error: string | null;
}

const initialState: TradesState = {
  trades: [],
  stats: null,
  dailyStats: [],
  symbolStats: [],
  botStats: [],
  isLoading: false,
  error: null,
};

// Async thunks
export const fetchTrades = createAsyncThunk(
  'trades/fetchTrades',
  async (params: any = {}, { rejectWithValue }) => {
    try {
      const response = await tradesAPI.getTrades(params);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.detail || 'Failed to fetch trades');
    }
  }
);

export const fetchTradeStats = createAsyncThunk(
  'trades/fetchTradeStats',
  async (params: any = {}, { rejectWithValue }) => {
    try {
      const response = await tradesAPI.getTradeStats(params);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.detail || 'Failed to fetch trade stats');
    }
  }
);

export const fetchDailyStats = createAsyncThunk(
  'trades/fetchDailyStats',
  async (days: number = 30, { rejectWithValue }) => {
    try {
      const response = await tradesAPI.getDailyStats(days);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.detail || 'Failed to fetch daily stats');
    }
  }
);

export const fetchStatsBySymbol = createAsyncThunk(
  'trades/fetchStatsBySymbol',
  async (days: number = 30, { rejectWithValue }) => {
    try {
      const response = await tradesAPI.getStatsBySymbol(days);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.detail || 'Failed to fetch symbol stats');
    }
  }
);

export const fetchStatsByBot = createAsyncThunk(
  'trades/fetchStatsByBot',
  async (days: number = 30, { rejectWithValue }) => {
    try {
      const response = await tradesAPI.getStatsByBot(days);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.detail || 'Failed to fetch bot stats');
    }
  }
);

export const exportTrades = createAsyncThunk(
  'trades/exportTrades',
  async (days: number = 30, { rejectWithValue }) => {
    try {
      const response = await tradesAPI.exportTrades(days);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.detail || 'Failed to export trades');
    }
  }
);

const tradesSlice = createSlice({
  name: 'trades',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    addTrade: (state, action) => {
      state.trades.unshift(action.payload);
    },
    updateTrade: (state, action) => {
      const index = state.trades.findIndex(trade => trade.id === action.payload.id);
      if (index !== -1) {
        state.trades[index] = action.payload;
      }
    },
  },
  extraReducers: (builder) => {
    // Fetch Trades
    builder
      .addCase(fetchTrades.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchTrades.fulfilled, (state, action) => {
        state.isLoading = false;
        state.trades = action.payload;
        state.error = null;
      })
      .addCase(fetchTrades.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Fetch Trade Stats
    builder
      .addCase(fetchTradeStats.fulfilled, (state, action) => {
        state.stats = action.payload;
      });

    // Fetch Daily Stats
    builder
      .addCase(fetchDailyStats.fulfilled, (state, action) => {
        state.dailyStats = action.payload;
      });

    // Fetch Stats by Symbol
    builder
      .addCase(fetchStatsBySymbol.fulfilled, (state, action) => {
        state.symbolStats = action.payload;
      });

    // Fetch Stats by Bot
    builder
      .addCase(fetchStatsByBot.fulfilled, (state, action) => {
        state.botStats = action.payload;
      });
  },
});

export const { clearError, addTrade, updateTrade } = tradesSlice.actions;
export default tradesSlice.reducer;
