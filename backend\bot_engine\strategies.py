"""
Trading strategies for the bot engine
"""
import pandas as pd
import numpy as np
import ta
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum

from ..models.trading import TradeType


class SignalStrength(Enum):
    WEAK = 30
    MODERATE = 50
    STRONG = 70
    VERY_STRONG = 90


@dataclass
class TradingSignal:
    """Trading signal data class"""
    signal_type: TradeType
    strength: float  # 0-100
    confidence: float  # 0-100
    price: float
    indicators: Dict[str, Any]
    reason: str


class BaseStrategy(ABC):
    """Base class for all trading strategies"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
    @abstractmethod
    async def generate_signal(self, current_price: float, data: pd.DataFrame) -> Optional[TradingSignal]:
        """Generate trading signal based on market data"""
        pass
    
    def _calculate_rsi(self, data: pd.DataFrame, period: int = 14) -> pd.Series:
        """Calculate RSI indicator"""
        return ta.momentum.RSIIndicator(data['close'], window=period).rsi()
    
    def _calculate_macd(self, data: pd.DataFrame) -> Dict[str, pd.Series]:
        """Calculate MACD indicator"""
        macd = ta.trend.MACD(data['close'])
        return {
            'macd': macd.macd(),
            'signal': macd.macd_signal(),
            'histogram': macd.macd_diff()
        }
    
    def _calculate_bollinger_bands(self, data: pd.DataFrame, period: int = 20) -> Dict[str, pd.Series]:
        """Calculate Bollinger Bands"""
        bb = ta.volatility.BollingerBands(data['close'], window=period)
        return {
            'upper': bb.bollinger_hband(),
            'middle': bb.bollinger_mavg(),
            'lower': bb.bollinger_lband()
        }
    
    def _calculate_sma(self, data: pd.DataFrame, period: int) -> pd.Series:
        """Calculate Simple Moving Average"""
        return ta.trend.SMAIndicator(data['close'], window=period).sma_indicator()
    
    def _calculate_ema(self, data: pd.DataFrame, period: int) -> pd.Series:
        """Calculate Exponential Moving Average"""
        return ta.trend.EMAIndicator(data['close'], window=period).ema_indicator()


class RSIStrategy(BaseStrategy):
    """RSI-based trading strategy"""
    
    async def generate_signal(self, current_price: float, data: pd.DataFrame) -> Optional[TradingSignal]:
        if len(data) < 20:
            return None
        
        # Get configuration
        rsi_period = self.config.get('rsi_period', 14)
        oversold_threshold = self.config.get('oversold_threshold', 30)
        overbought_threshold = self.config.get('overbought_threshold', 70)
        
        # Calculate RSI
        rsi = self._calculate_rsi(data, rsi_period)
        current_rsi = rsi.iloc[-1]
        
        indicators = {'rsi': current_rsi}
        
        # Generate signals
        if current_rsi < oversold_threshold:
            strength = min(100, (oversold_threshold - current_rsi) * 2)
            return TradingSignal(
                signal_type=TradeType.BUY,
                strength=strength,
                confidence=80,
                price=current_price,
                indicators=indicators,
                reason=f"RSI oversold at {current_rsi:.2f}"
            )
        elif current_rsi > overbought_threshold:
            strength = min(100, (current_rsi - overbought_threshold) * 2)
            return TradingSignal(
                signal_type=TradeType.SELL,
                strength=strength,
                confidence=80,
                price=current_price,
                indicators=indicators,
                reason=f"RSI overbought at {current_rsi:.2f}"
            )
        
        return None


class MACDStrategy(BaseStrategy):
    """MACD-based trading strategy"""
    
    async def generate_signal(self, current_price: float, data: pd.DataFrame) -> Optional[TradingSignal]:
        if len(data) < 30:
            return None
        
        # Calculate MACD
        macd_data = self._calculate_macd(data)
        
        current_macd = macd_data['macd'].iloc[-1]
        current_signal = macd_data['signal'].iloc[-1]
        current_histogram = macd_data['histogram'].iloc[-1]
        
        prev_macd = macd_data['macd'].iloc[-2]
        prev_signal = macd_data['signal'].iloc[-2]
        
        indicators = {
            'macd': current_macd,
            'signal': current_signal,
            'histogram': current_histogram
        }
        
        # MACD crossover signals
        if prev_macd <= prev_signal and current_macd > current_signal:
            # Bullish crossover
            strength = min(100, abs(current_histogram) * 50)
            return TradingSignal(
                signal_type=TradeType.BUY,
                strength=strength,
                confidence=75,
                price=current_price,
                indicators=indicators,
                reason="MACD bullish crossover"
            )
        elif prev_macd >= prev_signal and current_macd < current_signal:
            # Bearish crossover
            strength = min(100, abs(current_histogram) * 50)
            return TradingSignal(
                signal_type=TradeType.SELL,
                strength=strength,
                confidence=75,
                price=current_price,
                indicators=indicators,
                reason="MACD bearish crossover"
            )
        
        return None


class MovingAverageCrossoverStrategy(BaseStrategy):
    """Moving Average Crossover strategy"""
    
    async def generate_signal(self, current_price: float, data: pd.DataFrame) -> Optional[TradingSignal]:
        if len(data) < 50:
            return None
        
        # Get configuration
        fast_period = self.config.get('fast_period', 10)
        slow_period = self.config.get('slow_period', 30)
        
        # Calculate moving averages
        fast_ma = self._calculate_sma(data, fast_period)
        slow_ma = self._calculate_sma(data, slow_period)
        
        current_fast = fast_ma.iloc[-1]
        current_slow = slow_ma.iloc[-1]
        prev_fast = fast_ma.iloc[-2]
        prev_slow = slow_ma.iloc[-2]
        
        indicators = {
            'fast_ma': current_fast,
            'slow_ma': current_slow
        }
        
        # Crossover signals
        if prev_fast <= prev_slow and current_fast > current_slow:
            # Golden cross (bullish)
            strength = min(100, ((current_fast - current_slow) / current_slow) * 1000)
            return TradingSignal(
                signal_type=TradeType.BUY,
                strength=strength,
                confidence=70,
                price=current_price,
                indicators=indicators,
                reason=f"Golden cross: {fast_period}MA above {slow_period}MA"
            )
        elif prev_fast >= prev_slow and current_fast < current_slow:
            # Death cross (bearish)
            strength = min(100, ((current_slow - current_fast) / current_fast) * 1000)
            return TradingSignal(
                signal_type=TradeType.SELL,
                strength=strength,
                confidence=70,
                price=current_price,
                indicators=indicators,
                reason=f"Death cross: {fast_period}MA below {slow_period}MA"
            )
        
        return None


class BollingerBandsStrategy(BaseStrategy):
    """Bollinger Bands strategy"""
    
    async def generate_signal(self, current_price: float, data: pd.DataFrame) -> Optional[TradingSignal]:
        if len(data) < 25:
            return None
        
        # Get configuration
        period = self.config.get('period', 20)
        std_dev = self.config.get('std_dev', 2)
        
        # Calculate Bollinger Bands
        bb = self._calculate_bollinger_bands(data, period)
        
        current_upper = bb['upper'].iloc[-1]
        current_lower = bb['lower'].iloc[-1]
        current_middle = bb['middle'].iloc[-1]
        
        indicators = {
            'bb_upper': current_upper,
            'bb_middle': current_middle,
            'bb_lower': current_lower,
            'bb_position': (current_price - current_lower) / (current_upper - current_lower)
        }
        
        # Generate signals based on band touches
        if current_price <= current_lower:
            # Price at or below lower band (oversold)
            strength = min(100, ((current_lower - current_price) / current_lower) * 500)
            return TradingSignal(
                signal_type=TradeType.BUY,
                strength=strength,
                confidence=65,
                price=current_price,
                indicators=indicators,
                reason="Price at lower Bollinger Band"
            )
        elif current_price >= current_upper:
            # Price at or above upper band (overbought)
            strength = min(100, ((current_price - current_upper) / current_upper) * 500)
            return TradingSignal(
                signal_type=TradeType.SELL,
                strength=strength,
                confidence=65,
                price=current_price,
                indicators=indicators,
                reason="Price at upper Bollinger Band"
            )
        
        return None


class GridStrategy(BaseStrategy):
    """Grid trading strategy"""
    
    async def generate_signal(self, current_price: float, data: pd.DataFrame) -> Optional[TradingSignal]:
        # Get configuration
        grid_size = self.config.get('grid_size', 0.01)  # 1% grid
        base_price = self.config.get('base_price', current_price)
        
        # Calculate grid levels
        grid_level = round((current_price - base_price) / (base_price * grid_size))
        
        indicators = {
            'grid_level': grid_level,
            'base_price': base_price,
            'grid_size': grid_size
        }
        
        # Generate signals based on grid position
        if grid_level < -1:  # Price dropped below grid
            return TradingSignal(
                signal_type=TradeType.BUY,
                strength=70,
                confidence=60,
                price=current_price,
                indicators=indicators,
                reason=f"Grid buy signal at level {grid_level}"
            )
        elif grid_level > 1:  # Price rose above grid
            return TradingSignal(
                signal_type=TradeType.SELL,
                strength=70,
                confidence=60,
                price=current_price,
                indicators=indicators,
                reason=f"Grid sell signal at level {grid_level}"
            )
        
        return None


class MeanReversionStrategy(BaseStrategy):
    """Mean reversion strategy"""
    
    async def generate_signal(self, current_price: float, data: pd.DataFrame) -> Optional[TradingSignal]:
        if len(data) < 30:
            return None
        
        # Get configuration
        lookback_period = self.config.get('lookback_period', 20)
        threshold = self.config.get('threshold', 2)  # Standard deviations
        
        # Calculate mean and standard deviation
        recent_prices = data['close'].tail(lookback_period)
        mean_price = recent_prices.mean()
        std_price = recent_prices.std()
        
        # Calculate z-score
        z_score = (current_price - mean_price) / std_price
        
        indicators = {
            'mean_price': mean_price,
            'std_price': std_price,
            'z_score': z_score
        }
        
        # Generate signals based on deviation from mean
        if z_score < -threshold:
            # Price significantly below mean
            strength = min(100, abs(z_score) * 25)
            return TradingSignal(
                signal_type=TradeType.BUY,
                strength=strength,
                confidence=70,
                price=current_price,
                indicators=indicators,
                reason=f"Mean reversion buy: z-score {z_score:.2f}"
            )
        elif z_score > threshold:
            # Price significantly above mean
            strength = min(100, abs(z_score) * 25)
            return TradingSignal(
                signal_type=TradeType.SELL,
                strength=strength,
                confidence=70,
                price=current_price,
                indicators=indicators,
                reason=f"Mean reversion sell: z-score {z_score:.2f}"
            )
        
        return None


class StrategyFactory:
    """Factory for creating trading strategies"""
    
    _strategies = {
        'rsi': RSIStrategy,
        'macd': MACDStrategy,
        'ma_crossover': MovingAverageCrossoverStrategy,
        'bollinger_bands': BollingerBandsStrategy,
        'grid': GridStrategy,
        'mean_reversion': MeanReversionStrategy
    }
    
    @classmethod
    def create_strategy(cls, strategy_type: str, config: Dict[str, Any]) -> BaseStrategy:
        """Create a strategy instance"""
        strategy_class = cls._strategies.get(strategy_type.lower())
        if not strategy_class:
            raise ValueError(f"Unknown strategy type: {strategy_type}")
        
        return strategy_class(config)
    
    @classmethod
    def get_available_strategies(cls) -> list:
        """Get list of available strategies"""
        return list(cls._strategies.keys())
