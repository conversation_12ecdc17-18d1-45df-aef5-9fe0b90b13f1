"""
WebSocket API endpoints for real-time communication
"""
import json
import asyncio
from typing import Dict, Set
from fastapi import API<PERSON>outer, WebSocket, WebSocketDisconnect, Depends, HTTPException
from sqlalchemy.orm import Session

from ..core.database import get_db, get_redis
from ..core.security import verify_token
from ..models.user import User

router = APIRouter()


class ConnectionManager:
    """Manages WebSocket connections"""
    
    def __init__(self):
        # Store connections by user_id
        self.active_connections: Dict[int, Set[WebSocket]] = {}
        # Store user info for each connection
        self.connection_users: Dict[WebSocket, User] = {}
    
    async def connect(self, websocket: WebSocket, user: User):
        """Accept a new WebSocket connection"""
        await websocket.accept()
        
        if user.id not in self.active_connections:
            self.active_connections[user.id] = set()
        
        self.active_connections[user.id].add(websocket)
        self.connection_users[websocket] = user
        
        # Send welcome message
        await self.send_personal_message({
            "type": "connection_established",
            "message": "Connected to NeuroTrade OS",
            "user_id": user.id
        }, websocket)
    
    def disconnect(self, websocket: WebSocket):
        """Remove a WebSocket connection"""
        if websocket in self.connection_users:
            user = self.connection_users[websocket]
            
            # Remove from user's connections
            if user.id in self.active_connections:
                self.active_connections[user.id].discard(websocket)
                
                # Remove user entry if no more connections
                if not self.active_connections[user.id]:
                    del self.active_connections[user.id]
            
            # Remove from connection users
            del self.connection_users[websocket]
    
    async def send_personal_message(self, message: dict, websocket: WebSocket):
        """Send message to a specific WebSocket"""
        try:
            await websocket.send_text(json.dumps(message))
        except Exception:
            # Connection might be closed
            self.disconnect(websocket)
    
    async def send_to_user(self, message: dict, user_id: int):
        """Send message to all connections of a specific user"""
        if user_id in self.active_connections:
            disconnected = []
            for websocket in self.active_connections[user_id].copy():
                try:
                    await websocket.send_text(json.dumps(message))
                except Exception:
                    disconnected.append(websocket)
            
            # Clean up disconnected websockets
            for websocket in disconnected:
                self.disconnect(websocket)
    
    async def broadcast_to_all(self, message: dict):
        """Broadcast message to all connected users"""
        for user_id in list(self.active_connections.keys()):
            await self.send_to_user(message, user_id)
    
    async def broadcast_to_admins(self, message: dict):
        """Broadcast message to admin users only"""
        from ..models.user import UserRole
        
        for websocket, user in self.connection_users.items():
            if user.role == UserRole.ADMIN:
                await self.send_personal_message(message, websocket)
    
    def get_user_connection_count(self, user_id: int) -> int:
        """Get number of active connections for a user"""
        return len(self.active_connections.get(user_id, set()))
    
    def get_total_connections(self) -> int:
        """Get total number of active connections"""
        return sum(len(connections) for connections in self.active_connections.values())


# Global connection manager
manager = ConnectionManager()


async def get_current_user_ws(
    websocket: WebSocket,
    token: str,
    db: Session = Depends(get_db)
) -> User:
    """Get current user from WebSocket token"""
    # Verify token
    payload = verify_token(token)
    if not payload or payload.get("type") != "access":
        await websocket.close(code=4001, reason="Invalid token")
        raise HTTPException(status_code=401, detail="Invalid token")
    
    # Get user
    user_id = payload.get("sub")
    if not user_id:
        await websocket.close(code=4001, reason="Invalid token payload")
        raise HTTPException(status_code=401, detail="Invalid token payload")
    
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        await websocket.close(code=4001, reason="User not found")
        raise HTTPException(status_code=401, detail="User not found")
    
    return user


@router.websocket("/connect")
async def websocket_endpoint(
    websocket: WebSocket,
    token: str,
    db: Session = Depends(get_db)
):
    """Main WebSocket endpoint"""
    try:
        # Authenticate user
        user = await get_current_user_ws(websocket, token, db)
        
        # Connect user
        await manager.connect(websocket, user)
        
        # Send initial data
        await send_initial_data(websocket, user, db)
        
        # Listen for messages
        while True:
            try:
                data = await websocket.receive_text()
                message = json.loads(data)
                await handle_websocket_message(websocket, user, message, db)
            except WebSocketDisconnect:
                break
            except json.JSONDecodeError:
                await manager.send_personal_message({
                    "type": "error",
                    "message": "Invalid JSON format"
                }, websocket)
            except Exception as e:
                await manager.send_personal_message({
                    "type": "error",
                    "message": f"Error processing message: {str(e)}"
                }, websocket)
    
    except Exception as e:
        # Authentication or connection error
        try:
            await websocket.close(code=4001, reason=str(e))
        except:
            pass
    
    finally:
        manager.disconnect(websocket)


async def send_initial_data(websocket: WebSocket, user: User, db: Session):
    """Send initial data when user connects"""
    from ..models.trading import TradingBot, BotStatus
    from ..models.notifications import Notification
    
    # Get user's bots
    bots = db.query(TradingBot).filter(TradingBot.user_id == user.id).all()
    
    # Get unread notifications
    unread_notifications = db.query(Notification).filter(
        Notification.user_id == user.id,
        Notification.is_read == False
    ).count()
    
    await manager.send_personal_message({
        "type": "initial_data",
        "data": {
            "user": {
                "id": user.id,
                "email": user.email,
                "username": user.username,
                "role": user.role.value
            },
            "bots": [
                {
                    "id": bot.id,
                    "name": bot.name,
                    "status": bot.status.value,
                    "symbol": bot.symbol,
                    "exchange": bot.exchange,
                    "total_trades": bot.total_trades,
                    "total_profit_loss": bot.total_profit_loss
                }
                for bot in bots
            ],
            "unread_notifications": unread_notifications
        }
    }, websocket)


async def handle_websocket_message(websocket: WebSocket, user: User, message: dict, db: Session):
    """Handle incoming WebSocket messages"""
    message_type = message.get("type")
    
    if message_type == "ping":
        await manager.send_personal_message({
            "type": "pong",
            "timestamp": message.get("timestamp")
        }, websocket)
    
    elif message_type == "subscribe":
        # Subscribe to specific data feeds
        channels = message.get("channels", [])
        await handle_subscription(websocket, user, channels)
    
    elif message_type == "unsubscribe":
        # Unsubscribe from data feeds
        channels = message.get("channels", [])
        await handle_unsubscription(websocket, user, channels)
    
    elif message_type == "bot_action":
        # Handle bot control actions
        await handle_bot_action(websocket, user, message, db)
    
    else:
        await manager.send_personal_message({
            "type": "error",
            "message": f"Unknown message type: {message_type}"
        }, websocket)


async def handle_subscription(websocket: WebSocket, user: User, channels: list):
    """Handle subscription to data channels"""
    # Store subscriptions in Redis or memory
    redis_client = get_redis()
    
    for channel in channels:
        if channel.startswith("price:"):
            # Subscribe to price updates
            redis_client.sadd(f"ws_sub:{channel}", user.id)
        elif channel.startswith("bot:"):
            # Subscribe to bot updates
            bot_id = channel.split(":")[1]
            # Verify user owns the bot
            from ..models.trading import TradingBot
            from ..core.database import SessionLocal
            
            db = SessionLocal()
            try:
                bot = db.query(TradingBot).filter(
                    TradingBot.id == bot_id,
                    TradingBot.user_id == user.id
                ).first()
                
                if bot:
                    redis_client.sadd(f"ws_sub:{channel}", user.id)
                else:
                    await manager.send_personal_message({
                        "type": "error",
                        "message": f"Access denied to channel: {channel}"
                    }, websocket)
            finally:
                db.close()
    
    await manager.send_personal_message({
        "type": "subscription_confirmed",
        "channels": channels
    }, websocket)


async def handle_unsubscription(websocket: WebSocket, user: User, channels: list):
    """Handle unsubscription from data channels"""
    redis_client = get_redis()
    
    for channel in channels:
        redis_client.srem(f"ws_sub:{channel}", user.id)
    
    await manager.send_personal_message({
        "type": "unsubscription_confirmed",
        "channels": channels
    }, websocket)


async def handle_bot_action(websocket: WebSocket, user: User, message: dict, db: Session):
    """Handle bot control actions via WebSocket"""
    action = message.get("action")
    bot_id = message.get("bot_id")
    
    if not bot_id:
        await manager.send_personal_message({
            "type": "error",
            "message": "bot_id is required"
        }, websocket)
        return
    
    # Verify user owns the bot
    from ..models.trading import TradingBot
    bot = db.query(TradingBot).filter(
        TradingBot.id == bot_id,
        TradingBot.user_id == user.id
    ).first()
    
    if not bot:
        await manager.send_personal_message({
            "type": "error",
            "message": "Bot not found or access denied"
        }, websocket)
        return
    
    # Execute action
    from ..services.bot_engine import BotEngineService
    bot_engine = BotEngineService()
    
    success = False
    if action == "start":
        success = await bot_engine.start_bot(bot_id)
    elif action == "stop":
        success = await bot_engine.stop_bot(bot_id)
    elif action == "pause":
        success = await bot_engine.pause_bot(bot_id)
    elif action == "resume":
        success = await bot_engine.resume_bot(bot_id)
    
    await manager.send_personal_message({
        "type": "bot_action_result",
        "bot_id": bot_id,
        "action": action,
        "success": success
    }, websocket)


# Functions to send real-time updates
async def send_price_update(symbol: str, exchange: str, price_data: dict):
    """Send price update to subscribed users"""
    channel = f"price:{exchange}:{symbol}"
    redis_client = get_redis()
    
    # Get subscribed users
    subscribed_users = redis_client.smembers(f"ws_sub:{channel}")
    
    message = {
        "type": "price_update",
        "channel": channel,
        "data": price_data
    }
    
    for user_id in subscribed_users:
        await manager.send_to_user(message, int(user_id))


async def send_bot_update(bot_id: int, bot_data: dict):
    """Send bot update to subscribed users"""
    channel = f"bot:{bot_id}"
    redis_client = get_redis()
    
    # Get subscribed users
    subscribed_users = redis_client.smembers(f"ws_sub:{channel}")
    
    message = {
        "type": "bot_update",
        "channel": channel,
        "data": bot_data
    }
    
    for user_id in subscribed_users:
        await manager.send_to_user(message, int(user_id))


async def send_trade_notification(user_id: int, trade_data: dict):
    """Send trade notification to user"""
    message = {
        "type": "trade_notification",
        "data": trade_data
    }
    
    await manager.send_to_user(message, user_id)


async def send_system_notification(notification_data: dict, admin_only: bool = False):
    """Send system notification"""
    message = {
        "type": "system_notification",
        "data": notification_data
    }
    
    if admin_only:
        await manager.broadcast_to_admins(message)
    else:
        await manager.broadcast_to_all(message)


# WebSocket status endpoint
@router.get("/status")
async def websocket_status():
    """Get WebSocket connection status"""
    return {
        "total_connections": manager.get_total_connections(),
        "connected_users": len(manager.active_connections),
        "status": "healthy"
    }
