"""
Authentication API endpoints
"""
from datetime import datetime, timedelta
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from pydantic import BaseModel, EmailStr

from ..core.database import get_db, get_redis
from ..core.security import (
    verify_password, get_password_hash, create_access_token, create_refresh_token,
    verify_token, generate_hwid, verify_hwid, TOTPManager, generate_session_id
)
from ..core.config import settings
from ..models.user import User, UserSession, UserStatus, UserRole
from ..services.audit import AuditService

router = APIRouter()
security = HTTPBearer()


# Pydantic models
class LoginRequest(BaseModel):
    email: EmailStr
    password: str
    totp_code: Optional[str] = None


class RegisterRequest(BaseModel):
    email: EmailStr
    username: str
    password: str
    first_name: Optional[str] = None
    last_name: Optional[str] = None


class TokenResponse(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int


class RefreshTokenRequest(BaseModel):
    refresh_token: str


class Setup2FAResponse(BaseModel):
    secret: str
    qr_url: str


class Verify2FARequest(BaseModel):
    secret: str
    totp_code: str


class ChangePasswordRequest(BaseModel):
    current_password: str
    new_password: str


# Dependency to get current user
async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db),
    redis_client = Depends(get_redis)
) -> User:
    """Get current authenticated user"""
    token = credentials.credentials
    
    # Verify token
    payload = verify_token(token)
    if not payload or payload.get("type") != "access":
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # Get user
    user_id = payload.get("sub")
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token payload"
        )
    
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found"
        )
    
    # Check if user is active
    if user.status != UserStatus.ACTIVE:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User account is not active"
        )
    
    # Check session
    session_id = payload.get("session_id")
    if session_id:
        session_key = f"session:{session_id}"
        session_data = redis_client.get(session_key)
        if not session_data:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Session expired"
            )
    
    return user


# Dependency to get current admin user
async def get_current_admin_user(current_user: User = Depends(get_current_user)) -> User:
    """Get current authenticated admin user"""
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access required"
        )
    return current_user


@router.post("/register", response_model=dict)
async def register(
    request: RegisterRequest,
    req: Request,
    db: Session = Depends(get_db)
):
    """Register a new user"""
    # Check if user already exists
    existing_user = db.query(User).filter(
        (User.email == request.email) | (User.username == request.username)
    ).first()
    
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User with this email or username already exists"
        )
    
    # Generate HWID
    hwid = generate_hwid()
    
    # Create new user
    user = User(
        email=request.email,
        username=request.username,
        hashed_password=get_password_hash(request.password),
        first_name=request.first_name,
        last_name=request.last_name,
        hwid=hwid,
        role=UserRole.TRADER,
        status=UserStatus.PENDING
    )
    
    db.add(user)
    db.commit()
    db.refresh(user)
    
    # Log registration
    await AuditService.log_action(
        user_id=user.id,
        action="user_registered",
        ip_address=req.client.host,
        user_agent=req.headers.get("user-agent"),
        success=True
    )
    
    return {
        "message": "User registered successfully",
        "user_id": user.id,
        "hwid": hwid
    }


@router.post("/login", response_model=TokenResponse)
async def login(
    request: LoginRequest,
    req: Request,
    db: Session = Depends(get_db),
    redis_client = Depends(get_redis)
):
    """Login user"""
    # Get user
    user = db.query(User).filter(User.email == request.email).first()
    if not user or not verify_password(request.password, user.hashed_password):
        await AuditService.log_action(
            action="login_failed",
            details=f"Invalid credentials for {request.email}",
            ip_address=req.client.host,
            user_agent=req.headers.get("user-agent"),
            success=False
        )
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid email or password"
        )
    
    # Check user status
    if user.status != UserStatus.ACTIVE:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Account is not active"
        )
    
    # Verify HWID
    current_hwid = generate_hwid()
    if user.hwid and not verify_hwid(user.hwid, current_hwid):
        await AuditService.log_action(
            user_id=user.id,
            action="login_failed",
            details="HWID mismatch",
            ip_address=req.client.host,
            user_agent=req.headers.get("user-agent"),
            success=False
        )
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Hardware ID mismatch. Please contact support."
        )
    
    # Check 2FA if enabled
    if user.is_2fa_enabled:
        if not request.totp_code:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="2FA code required"
            )
        
        if not TOTPManager.verify_2fa(user.totp_secret, request.totp_code):
            await AuditService.log_action(
                user_id=user.id,
                action="login_failed",
                details="Invalid 2FA code",
                ip_address=req.client.host,
                user_agent=req.headers.get("user-agent"),
                success=False
            )
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid 2FA code"
            )
    
    # Generate session
    session_id = generate_session_id()
    
    # Create session in database
    session = UserSession(
        user_id=user.id,
        session_id=session_id,
        hwid=current_hwid,
        ip_address=req.client.host,
        user_agent=req.headers.get("user-agent"),
        expires_at=datetime.utcnow() + timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS)
    )
    db.add(session)
    
    # Update user last login
    user.last_login = datetime.utcnow()
    user.session_id = session_id
    
    db.commit()
    
    # Store session in Redis
    session_key = f"session:{session_id}"
    redis_client.setex(
        session_key,
        timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS),
        f"{user.id}:{current_hwid}"
    )
    
    # Create tokens
    token_data = {
        "sub": str(user.id),
        "email": user.email,
        "role": user.role.value,
        "session_id": session_id
    }
    
    access_token = create_access_token(token_data)
    refresh_token = create_refresh_token(token_data)
    
    # Log successful login
    await AuditService.log_action(
        user_id=user.id,
        action="login_success",
        ip_address=req.client.host,
        user_agent=req.headers.get("user-agent"),
        session_id=session_id,
        success=True
    )
    
    return TokenResponse(
        access_token=access_token,
        refresh_token=refresh_token,
        expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
    )


@router.post("/refresh", response_model=TokenResponse)
async def refresh_token(
    request: RefreshTokenRequest,
    db: Session = Depends(get_db),
    redis_client = Depends(get_redis)
):
    """Refresh access token"""
    # Verify refresh token
    payload = verify_token(request.refresh_token)
    if not payload or payload.get("type") != "refresh":
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid refresh token"
        )
    
    # Get user
    user_id = payload.get("sub")
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found"
        )
    
    # Check session
    session_id = payload.get("session_id")
    if session_id:
        session_key = f"session:{session_id}"
        session_data = redis_client.get(session_key)
        if not session_data:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Session expired"
            )
    
    # Create new access token
    token_data = {
        "sub": str(user.id),
        "email": user.email,
        "role": user.role.value,
        "session_id": session_id
    }
    
    access_token = create_access_token(token_data)
    
    return TokenResponse(
        access_token=access_token,
        refresh_token=request.refresh_token,  # Keep the same refresh token
        expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
    )


@router.post("/logout")
async def logout(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    redis_client = Depends(get_redis)
):
    """Logout user"""
    # Invalidate session
    if current_user.session_id:
        session_key = f"session:{current_user.session_id}"
        redis_client.delete(session_key)
        
        # Update session in database
        session = db.query(UserSession).filter(
            UserSession.session_id == current_user.session_id
        ).first()
        if session:
            session.is_active = False
            db.commit()
    
    return {"message": "Logged out successfully"}


@router.get("/me")
async def get_current_user_info(current_user: User = Depends(get_current_user)):
    """Get current user information"""
    return {
        "id": current_user.id,
        "email": current_user.email,
        "username": current_user.username,
        "first_name": current_user.first_name,
        "last_name": current_user.last_name,
        "role": current_user.role.value,
        "status": current_user.status.value,
        "is_2fa_enabled": current_user.is_2fa_enabled,
        "is_email_verified": current_user.is_email_verified,
        "created_at": current_user.created_at,
        "last_login": current_user.last_login
    }


@router.post("/setup-2fa", response_model=Setup2FAResponse)
async def setup_2fa(current_user: User = Depends(get_current_user)):
    """Setup 2FA for user"""
    if current_user.is_2fa_enabled:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="2FA is already enabled"
        )
    
    # Generate 2FA setup
    setup_data = TOTPManager.setup_2fa(current_user.email)
    
    return Setup2FAResponse(
        secret=setup_data["secret"],
        qr_url=setup_data["qr_url"]
    )


@router.post("/verify-2fa")
async def verify_2fa(
    request: Verify2FARequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Verify and enable 2FA"""
    if current_user.is_2fa_enabled:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="2FA is already enabled"
        )
    
    # Verify TOTP code
    if not TOTPManager.verify_2fa(request.secret, request.totp_code):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid 2FA code"
        )
    
    # Enable 2FA
    current_user.totp_secret = request.secret
    current_user.is_2fa_enabled = True
    db.commit()
    
    return {"message": "2FA enabled successfully"}


@router.post("/disable-2fa")
async def disable_2fa(
    totp_code: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Disable 2FA"""
    if not current_user.is_2fa_enabled:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="2FA is not enabled"
        )
    
    # Verify TOTP code
    if not TOTPManager.verify_2fa(current_user.totp_secret, totp_code):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid 2FA code"
        )
    
    # Disable 2FA
    current_user.totp_secret = None
    current_user.is_2fa_enabled = False
    db.commit()
    
    return {"message": "2FA disabled successfully"}


@router.post("/change-password")
async def change_password(
    request: ChangePasswordRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Change user password"""
    # Verify current password
    if not verify_password(request.current_password, current_user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid current password"
        )
    
    # Update password
    current_user.hashed_password = get_password_hash(request.new_password)
    db.commit()
    
    return {"message": "Password changed successfully"}
