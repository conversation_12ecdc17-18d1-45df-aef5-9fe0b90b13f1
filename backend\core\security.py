"""
Security utilities including JWT, HWID, TOTP, and encryption
"""
import hashlib
import platform
import subprocess
import uuid
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
import pyotp
from jose import JWTError, jwt
from passlib.context import Crypt<PERSON>ontext
from cryptography.fernet import Fernet
from .config import settings

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Encryption for API keys
encryption_key = Fernet.generate_key()
cipher_suite = Fernet(encryption_key)


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password against its hash"""
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """Hash a password"""
    return pwd_context.hash(password)


def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
    """Create JWT access token"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode.update({"exp": expire, "type": "access"})
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return encoded_jwt


def create_refresh_token(data: Dict[str, Any]) -> str:
    """Create JWT refresh token"""
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS)
    to_encode.update({"exp": expire, "type": "refresh"})
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return encoded_jwt


def verify_token(token: str) -> Optional[Dict[str, Any]]:
    """Verify and decode JWT token"""
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        return payload
    except JWTError:
        return None


def generate_hwid() -> str:
    """Generate Hardware ID based on system characteristics"""
    try:
        # Get MAC address
        mac = ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff) 
                       for elements in range(0, 2*6, 2)][::-1])
        
        # Get CPU info
        cpu_info = platform.processor()
        
        # Get system info
        system_info = f"{platform.system()}-{platform.release()}"
        
        # Combine all info
        hwid_string = f"{mac}-{cpu_info}-{system_info}"
        
        # Hash the combined string
        hwid_hash = hashlib.sha256(hwid_string.encode()).hexdigest()
        
        return hwid_hash[:32]  # Return first 32 characters
    except Exception:
        # Fallback to a random UUID if hardware detection fails
        return str(uuid.uuid4()).replace('-', '')[:32]


def verify_hwid(stored_hwid: str, current_hwid: str) -> bool:
    """Verify if current HWID matches stored HWID"""
    return stored_hwid == current_hwid


def generate_totp_secret() -> str:
    """Generate TOTP secret for 2FA"""
    return pyotp.random_base32()


def generate_totp_qr_url(secret: str, email: str, issuer: str = "NeuroTrade OS") -> str:
    """Generate TOTP QR code URL"""
    totp = pyotp.TOTP(secret)
    return totp.provisioning_uri(
        name=email,
        issuer_name=issuer
    )


def verify_totp(secret: str, token: str) -> bool:
    """Verify TOTP token"""
    totp = pyotp.TOTP(secret)
    return totp.verify(token, valid_window=1)


def encrypt_api_key(api_key: str) -> str:
    """Encrypt API key for secure storage"""
    return cipher_suite.encrypt(api_key.encode()).decode()


def decrypt_api_key(encrypted_key: str) -> str:
    """Decrypt API key"""
    return cipher_suite.decrypt(encrypted_key.encode()).decode()


def generate_session_id() -> str:
    """Generate unique session ID"""
    return str(uuid.uuid4())


def hash_string(text: str) -> str:
    """Hash a string using SHA256"""
    return hashlib.sha256(text.encode()).hexdigest()


class HWIDManager:
    """Hardware ID management class"""
    
    @staticmethod
    def get_system_hwid() -> str:
        """Get current system HWID"""
        return generate_hwid()
    
    @staticmethod
    def is_hwid_valid(user_hwid: str) -> bool:
        """Check if user HWID is valid"""
        current_hwid = generate_hwid()
        return verify_hwid(user_hwid, current_hwid)
    
    @staticmethod
    def register_hwid(user_id: int) -> str:
        """Register new HWID for user"""
        return generate_hwid()


class TOTPManager:
    """TOTP (2FA) management class"""
    
    @staticmethod
    def setup_2fa(email: str) -> Dict[str, str]:
        """Setup 2FA for user"""
        secret = generate_totp_secret()
        qr_url = generate_totp_qr_url(secret, email)
        return {
            "secret": secret,
            "qr_url": qr_url
        }
    
    @staticmethod
    def verify_2fa(secret: str, token: str) -> bool:
        """Verify 2FA token"""
        return verify_totp(secret, token)


class APIKeyManager:
    """API Key encryption/decryption manager"""
    
    @staticmethod
    def encrypt_key(api_key: str) -> str:
        """Encrypt API key"""
        return encrypt_api_key(api_key)
    
    @staticmethod
    def decrypt_key(encrypted_key: str) -> str:
        """Decrypt API key"""
        return decrypt_api_key(encrypted_key)
