"""
Bot engine service for managing trading bots
"""
import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Optional
from sqlalchemy.orm import Session

from ..core.database import SessionLocal
from ..models.trading import TradingBot, BotStatus, Trade, TradingSignal
from ..models.user import User
from ..bot_engine.strategies import StrategyFactory
from ..bot_engine.executor import TradeExecutor
from ..services.market_data import MarketDataService

logger = logging.getLogger(__name__)


class BotEngineService:
    """Service for managing and executing trading bots"""
    
    def __init__(self):
        self.running_bots: Dict[int, 'BotInstance'] = {}
        self.running = False
        self.market_data_service: Optional[MarketDataService] = None
        self.executor_task: Optional[asyncio.Task] = None
        
    async def start(self):
        """Start the bot engine service"""
        logger.info("Starting bot engine service...")
        
        self.running = True
        
        # Start the main execution loop
        self.executor_task = asyncio.create_task(self._execution_loop())
        
        # Load and start active bots
        await self._load_active_bots()
        
        logger.info("Bot engine service started")
    
    async def stop(self):
        """Stop the bot engine service"""
        logger.info("Stopping bot engine service...")
        
        self.running = False
        
        # Stop all running bots
        for bot_instance in self.running_bots.values():
            await bot_instance.stop()
        
        # Cancel executor task
        if self.executor_task:
            self.executor_task.cancel()
            try:
                await self.executor_task
            except asyncio.CancelledError:
                pass
        
        logger.info("Bot engine service stopped")
    
    async def _load_active_bots(self):
        """Load and start all active bots from database"""
        db = SessionLocal()
        try:
            active_bots = db.query(TradingBot).filter(
                TradingBot.status == BotStatus.RUNNING
            ).all()
            
            for bot in active_bots:
                await self.start_bot(bot.id)
                
        except Exception as e:
            logger.error(f"Error loading active bots: {e}")
        finally:
            db.close()
    
    async def _execution_loop(self):
        """Main execution loop for all bots"""
        while self.running:
            try:
                # Execute all running bots
                for bot_id, bot_instance in list(self.running_bots.items()):
                    try:
                        await bot_instance.execute()
                    except Exception as e:
                        logger.error(f"Error executing bot {bot_id}: {e}")
                        await self._handle_bot_error(bot_id, str(e))
                
                # Sleep for a short interval
                await asyncio.sleep(1)
                
            except Exception as e:
                logger.error(f"Error in bot execution loop: {e}")
                await asyncio.sleep(5)
    
    async def start_bot(self, bot_id: int) -> bool:
        """Start a specific bot"""
        if bot_id in self.running_bots:
            logger.warning(f"Bot {bot_id} is already running")
            return False
        
        db = SessionLocal()
        try:
            bot = db.query(TradingBot).filter(TradingBot.id == bot_id).first()
            if not bot:
                logger.error(f"Bot {bot_id} not found")
                return False
            
            # Create bot instance
            bot_instance = BotInstance(bot, self.market_data_service)
            await bot_instance.start()
            
            # Add to running bots
            self.running_bots[bot_id] = bot_instance
            
            # Update bot status in database
            bot.status = BotStatus.RUNNING
            bot.started_at = datetime.utcnow()
            db.commit()
            
            logger.info(f"Started bot {bot_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error starting bot {bot_id}: {e}")
            return False
        finally:
            db.close()
    
    async def stop_bot(self, bot_id: int) -> bool:
        """Stop a specific bot"""
        if bot_id not in self.running_bots:
            logger.warning(f"Bot {bot_id} is not running")
            return False
        
        try:
            # Stop bot instance
            bot_instance = self.running_bots[bot_id]
            await bot_instance.stop()
            
            # Remove from running bots
            del self.running_bots[bot_id]
            
            # Update bot status in database
            db = SessionLocal()
            try:
                bot = db.query(TradingBot).filter(TradingBot.id == bot_id).first()
                if bot:
                    bot.status = BotStatus.STOPPED
                    bot.stopped_at = datetime.utcnow()
                    db.commit()
            finally:
                db.close()
            
            logger.info(f"Stopped bot {bot_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error stopping bot {bot_id}: {e}")
            return False
    
    async def pause_bot(self, bot_id: int) -> bool:
        """Pause a specific bot"""
        if bot_id not in self.running_bots:
            return False
        
        try:
            bot_instance = self.running_bots[bot_id]
            bot_instance.paused = True
            
            # Update bot status in database
            db = SessionLocal()
            try:
                bot = db.query(TradingBot).filter(TradingBot.id == bot_id).first()
                if bot:
                    bot.status = BotStatus.PAUSED
                    db.commit()
            finally:
                db.close()
            
            return True
        except Exception as e:
            logger.error(f"Error pausing bot {bot_id}: {e}")
            return False
    
    async def resume_bot(self, bot_id: int) -> bool:
        """Resume a paused bot"""
        if bot_id not in self.running_bots:
            return False
        
        try:
            bot_instance = self.running_bots[bot_id]
            bot_instance.paused = False
            
            # Update bot status in database
            db = SessionLocal()
            try:
                bot = db.query(TradingBot).filter(TradingBot.id == bot_id).first()
                if bot:
                    bot.status = BotStatus.RUNNING
                    db.commit()
            finally:
                db.close()
            
            return True
        except Exception as e:
            logger.error(f"Error resuming bot {bot_id}: {e}")
            return False
    
    async def _handle_bot_error(self, bot_id: int, error_message: str):
        """Handle bot execution error"""
        try:
            # Stop the bot
            if bot_id in self.running_bots:
                await self.running_bots[bot_id].stop()
                del self.running_bots[bot_id]
            
            # Update bot status in database
            db = SessionLocal()
            try:
                bot = db.query(TradingBot).filter(TradingBot.id == bot_id).first()
                if bot:
                    bot.status = BotStatus.ERROR
                    db.commit()
            finally:
                db.close()
            
            logger.error(f"Bot {bot_id} encountered error: {error_message}")
            
        except Exception as e:
            logger.error(f"Error handling bot error: {e}")
    
    def get_bot_status(self, bot_id: int) -> Optional[Dict]:
        """Get current status of a bot"""
        if bot_id in self.running_bots:
            return self.running_bots[bot_id].get_status()
        return None
    
    def get_all_bot_statuses(self) -> Dict[int, Dict]:
        """Get status of all running bots"""
        return {
            bot_id: bot_instance.get_status()
            for bot_id, bot_instance in self.running_bots.items()
        }


class BotInstance:
    """Individual bot instance"""
    
    def __init__(self, bot: TradingBot, market_data_service: Optional[MarketDataService]):
        self.bot = bot
        self.market_data_service = market_data_service
        self.strategy = None
        self.executor = None
        self.paused = False
        self.last_execution = None
        self.execution_count = 0
        
    async def start(self):
        """Start the bot instance"""
        # Initialize strategy
        self.strategy = StrategyFactory.create_strategy(
            self.bot.strategy,
            self.bot.strategy_config
        )
        
        # Initialize executor
        self.executor = TradeExecutor(self.bot)
        
        logger.info(f"Bot instance {self.bot.id} started")
    
    async def stop(self):
        """Stop the bot instance"""
        self.paused = True
        logger.info(f"Bot instance {self.bot.id} stopped")
    
    async def execute(self):
        """Execute bot logic"""
        if self.paused:
            return
        
        try:
            # Check if it's time to execute
            now = datetime.utcnow()
            if (self.last_execution and 
                (now - self.last_execution).total_seconds() < self.bot.execution_interval):
                return
            
            # Get current market data
            if self.market_data_service:
                current_price = await self.market_data_service.get_current_price(
                    self.bot.exchange, self.bot.symbol
                )
                
                if current_price is None:
                    return
                
                # Get historical data for strategy
                historical_data = await self.market_data_service.get_historical_data(
                    self.bot.exchange, self.bot.symbol, '1m', 100
                )
                
                if historical_data is None or historical_data.empty:
                    return
                
                # Execute strategy
                signal = await self.strategy.generate_signal(
                    current_price, historical_data
                )
                
                if signal:
                    # Store signal in database
                    await self._store_signal(signal)
                    
                    # Execute trade if signal is strong enough
                    if signal.strength >= 70:  # Configurable threshold
                        await self.executor.execute_trade(signal)
                
                self.last_execution = now
                self.execution_count += 1
                
                # Update bot last execution time
                db = SessionLocal()
                try:
                    bot = db.query(TradingBot).filter(TradingBot.id == self.bot.id).first()
                    if bot:
                        bot.last_execution = now
                        db.commit()
                finally:
                    db.close()
                
        except Exception as e:
            logger.error(f"Error executing bot {self.bot.id}: {e}")
            raise
    
    async def _store_signal(self, signal):
        """Store trading signal in database"""
        db = SessionLocal()
        try:
            db_signal = TradingSignal(
                bot_id=self.bot.id,
                symbol=self.bot.symbol,
                signal_type=signal.signal_type,
                strength=signal.strength,
                confidence=signal.confidence,
                price=signal.price,
                indicators=signal.indicators,
                reason=signal.reason
            )
            db.add(db_signal)
            db.commit()
        except Exception as e:
            logger.error(f"Error storing signal: {e}")
        finally:
            db.close()
    
    def get_status(self) -> Dict:
        """Get current bot status"""
        return {
            "bot_id": self.bot.id,
            "status": "paused" if self.paused else "running",
            "last_execution": self.last_execution,
            "execution_count": self.execution_count,
            "strategy": self.bot.strategy.value,
            "symbol": self.bot.symbol,
            "exchange": self.bot.exchange
        }
