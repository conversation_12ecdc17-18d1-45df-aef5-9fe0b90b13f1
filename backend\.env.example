# NeuroTrade OS Backend Environment Configuration

# Environment
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO
LOG_FILE=/app/logs/neurotrade.log

# Database Configuration
DATABASE_URL=****************************************************************/neurotrade

# Redis Configuration
REDIS_URL=redis://:redis_secure_password@redis:6379/0

# InfluxDB Configuration
INFLUXDB_URL=http://influxdb:8086
INFLUXDB_TOKEN=neurotrade_influx_token_12345
INFLUXDB_ORG=neurotrade
INFLUXDB_BUCKET=market_data

# Security Configuration
SECRET_KEY=your_super_secret_key_change_in_production_12345
ENCRYPTION_KEY=your_encryption_key_32_chars_long_12345
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# CORS Configuration
CORS_ORIGINS=["http://localhost:3000", "http://frontend:3000"]

# Email Configuration (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password
SMTP_FROM=<EMAIL>

# Exchange API Configuration (Optional - for testing)
BINANCE_API_KEY=your_binance_api_key
BINANCE_SECRET_KEY=your_binance_secret_key
COINBASE_API_KEY=your_coinbase_api_key
COINBASE_SECRET_KEY=your_coinbase_secret_key
COINBASE_PASSPHRASE=your_coinbase_passphrase

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_BURST=10

# File Upload
MAX_FILE_SIZE=10485760  # 10MB
UPLOAD_DIR=/app/uploads

# Monitoring
SENTRY_DSN=your_sentry_dsn_here
PROMETHEUS_ENABLED=true

# Feature Flags
ENABLE_2FA=true
ENABLE_HWID=true
ENABLE_EMAIL_VERIFICATION=true
ENABLE_AUDIT_LOGGING=true
