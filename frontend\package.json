{"name": "neurotrade-frontend", "version": "1.0.0", "private": true, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@reduxjs/toolkit": "^1.9.7", "@types/node": "^20.8.10", "@types/react": "^18.2.33", "@types/react-dom": "^18.2.14", "axios": "^1.6.0", "chart.js": "^4.4.0", "chartjs-adapter-date-fns": "^3.0.0", "date-fns": "^2.30.0", "framer-motion": "^10.16.4", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.47.0", "react-hot-toast": "^2.4.1", "react-query": "^3.39.3", "react-redux": "^8.1.3", "react-router-dom": "^6.17.0", "react-scripts": "5.0.1", "socket.io-client": "^4.7.2", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "web-vitals": "^3.5.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/jest": "^29.5.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.31"}, "proxy": "http://localhost:8000"}