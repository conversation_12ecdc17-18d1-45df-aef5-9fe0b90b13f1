"""
User model and related schemas
"""
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, Float, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum
from ..core.database import Base


class UserRole(enum.Enum):
    ADMIN = "admin"
    TRADER = "trader"
    VIEWER = "viewer"


class UserStatus(enum.Enum):
    ACTIVE = "active"
    SUSPENDED = "suspended"
    BANNED = "banned"
    PENDING = "pending"


class User(Base):
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    email = Column(String(255), unique=True, index=True, nullable=False)
    username = Column(String(100), unique=True, index=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    
    # Profile Information
    first_name = Column(String(100))
    last_name = Column(String(100))
    phone = Column(String(20))
    
    # Security
    hwid = Column(String(64), index=True)  # Hardware ID
    totp_secret = Column(String(32))  # 2FA secret
    is_2fa_enabled = Column(Boolean, default=False)
    
    # Role and Status
    role = Column(Enum(UserRole), default=UserRole.TRADER)
    status = Column(Enum(UserStatus), default=UserStatus.PENDING)
    
    # Verification
    is_email_verified = Column(Boolean, default=False)
    email_verification_token = Column(String(255))
    
    # Session Management
    last_login = Column(DateTime(timezone=True))
    last_activity = Column(DateTime(timezone=True))
    session_id = Column(String(255))
    
    # Quotas and Limits
    max_bots = Column(Integer, default=5)
    max_concurrent_trades = Column(Integer, default=10)
    risk_limit_percentage = Column(Float, default=10.0)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    bots = relationship("TradingBot", back_populates="owner")
    trades = relationship("Trade", back_populates="user")
    api_keys = relationship("ExchangeAPIKey", back_populates="user")
    notifications = relationship("Notification", back_populates="user")
    audit_logs = relationship("AuditLog", back_populates="user")


class UserSession(Base):
    __tablename__ = "user_sessions"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, index=True, nullable=False)
    session_id = Column(String(255), unique=True, index=True, nullable=False)
    hwid = Column(String(64), nullable=False)
    ip_address = Column(String(45))  # IPv6 compatible
    user_agent = Column(Text)
    
    # Session Status
    is_active = Column(Boolean, default=True)
    last_activity = Column(DateTime(timezone=True), server_default=func.now())
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    expires_at = Column(DateTime(timezone=True))


class ExchangeAPIKey(Base):
    __tablename__ = "exchange_api_keys"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, index=True, nullable=False)
    
    # Exchange Information
    exchange_name = Column(String(50), nullable=False)  # binance, coinbase, etc.
    api_key = Column(Text, nullable=False)  # Encrypted
    secret_key = Column(Text, nullable=False)  # Encrypted
    passphrase = Column(Text)  # For exchanges like Coinbase Pro (Encrypted)
    
    # Configuration
    is_testnet = Column(Boolean, default=False)
    is_active = Column(Boolean, default=True)
    
    # Permissions
    can_trade = Column(Boolean, default=True)
    can_withdraw = Column(Boolean, default=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    user = relationship("User", back_populates="api_keys")


class AuditLog(Base):
    __tablename__ = "audit_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, index=True)
    
    # Action Information
    action = Column(String(100), nullable=False)  # login, trade, bot_start, etc.
    resource = Column(String(100))  # bot_id, trade_id, etc.
    details = Column(Text)  # JSON string with additional details
    
    # Request Information
    ip_address = Column(String(45))
    user_agent = Column(Text)
    session_id = Column(String(255))
    
    # Result
    success = Column(Boolean, default=True)
    error_message = Column(Text)
    
    # Timestamp
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    user = relationship("User", back_populates="audit_logs")
