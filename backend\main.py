"""
NeuroTrade OS - Main FastAPI Application
"""
import os
import logging
from contextlib import asynccontextmanager
from fastapi import <PERSON>AP<PERSON>, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
import uvicorn

# Import core modules
from .core.config import settings
from .core.database import init_db, close_db_connections

# Import API routers
from .api.auth import router as auth_router
from .api.users import router as users_router
from .api.bots import router as bots_router
from .api.trades import router as trades_router
from .api.exchanges import router as exchanges_router
from .api.notifications import router as notifications_router
from .api.admin import router as admin_router
from .api.websockets import router as websocket_router

# Import services
from .services.market_data import MarketDataService
from .services.bot_engine import BotEngineService

# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(settings.LOG_FILE),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    logger.info("Starting NeuroTrade OS...")
    
    # Initialize database
    init_db()
    logger.info("Database initialized")
    
    # Start background services
    market_data_service = MarketDataService()
    bot_engine_service = BotEngineService()
    
    await market_data_service.start()
    await bot_engine_service.start()
    
    logger.info("Background services started")
    logger.info("NeuroTrade OS started successfully")
    
    yield
    
    # Shutdown
    logger.info("Shutting down NeuroTrade OS...")
    
    # Stop background services
    await market_data_service.stop()
    await bot_engine_service.stop()
    
    # Close database connections
    close_db_connections()
    
    logger.info("NeuroTrade OS shutdown complete")


# Create FastAPI application
app = FastAPI(
    title="NeuroTrade OS",
    description="Advanced AI-driven cryptocurrency trading platform",
    version="1.0.0",
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
    lifespan=lifespan
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

if settings.ENVIRONMENT == "production":
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=["neurotrade.com", "*.neurotrade.com"]
    )


# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler"""
    logger.error(f"Global exception: {exc}", exc_info=True)
    
    if settings.DEBUG:
        return JSONResponse(
            status_code=500,
            content={
                "detail": str(exc),
                "type": type(exc).__name__
            }
        )
    else:
        return JSONResponse(
            status_code=500,
            content={"detail": "Internal server error"}
        )


# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "version": "1.0.0",
        "environment": settings.ENVIRONMENT
    }


# API version endpoint
@app.get("/api/v1/version")
async def get_version():
    """Get API version"""
    return {
        "version": "1.0.0",
        "name": "NeuroTrade OS",
        "description": "Advanced AI-driven cryptocurrency trading platform"
    }


# Include API routers
app.include_router(auth_router, prefix="/api/v1/auth", tags=["Authentication"])
app.include_router(users_router, prefix="/api/v1/users", tags=["Users"])
app.include_router(bots_router, prefix="/api/v1/bots", tags=["Trading Bots"])
app.include_router(trades_router, prefix="/api/v1/trades", tags=["Trades"])
app.include_router(exchanges_router, prefix="/api/v1/exchanges", tags=["Exchanges"])
app.include_router(notifications_router, prefix="/api/v1/notifications", tags=["Notifications"])
app.include_router(admin_router, prefix="/api/v1/admin", tags=["Admin"])
app.include_router(websocket_router, prefix="/ws", tags=["WebSockets"])


if __name__ == "__main__":
    uvicorn.run(
        "backend.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower()
    )
