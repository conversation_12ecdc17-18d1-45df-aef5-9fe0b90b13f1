import React, { useEffect } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';

import { RootState } from './store/store';
import { checkAuth } from './store/slices/authSlice';
import { WebSocketProvider } from './contexts/WebSocketContext';

// Components
import Layout from './components/Layout/Layout';
import ProtectedRoute from './components/Auth/ProtectedRoute';
import LoadingSpinner from './components/UI/LoadingSpinner';

// Pages
import LoginPage from './pages/Auth/LoginPage';
import RegisterPage from './pages/Auth/RegisterPage';
import DashboardPage from './pages/Dashboard/DashboardPage';
import BotsPage from './pages/Bots/BotsPage';
import BotDetailPage from './pages/Bots/BotDetailPage';
import CreateBotPage from './pages/Bots/CreateBotPage';
import TradesPage from './pages/Trades/TradesPage';
import ExchangesPage from './pages/Exchanges/ExchangesPage';
import NotificationsPage from './pages/Notifications/NotificationsPage';
import ProfilePage from './pages/Profile/ProfilePage';
import AdminPage from './pages/Admin/AdminPage';

function App() {
  const dispatch = useDispatch();
  const { isAuthenticated, isLoading, user } = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    // Check if user is authenticated on app load
    dispatch(checkAuth() as any);
  }, [dispatch]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="App">
      <Routes>
        {/* Public routes */}
        <Route 
          path="/login" 
          element={
            isAuthenticated ? <Navigate to="/dashboard" replace /> : <LoginPage />
          } 
        />
        <Route 
          path="/register" 
          element={
            isAuthenticated ? <Navigate to="/dashboard" replace /> : <RegisterPage />
          } 
        />

        {/* Protected routes */}
        <Route
          path="/*"
          element={
            <ProtectedRoute>
              <WebSocketProvider>
                <Layout>
                  <Routes>
                    <Route path="/" element={<Navigate to="/dashboard" replace />} />
                    <Route path="/dashboard" element={<DashboardPage />} />
                    
                    {/* Bots */}
                    <Route path="/bots" element={<BotsPage />} />
                    <Route path="/bots/create" element={<CreateBotPage />} />
                    <Route path="/bots/:id" element={<BotDetailPage />} />
                    
                    {/* Trades */}
                    <Route path="/trades" element={<TradesPage />} />
                    
                    {/* Exchanges */}
                    <Route path="/exchanges" element={<ExchangesPage />} />
                    
                    {/* Notifications */}
                    <Route path="/notifications" element={<NotificationsPage />} />
                    
                    {/* Profile */}
                    <Route path="/profile" element={<ProfilePage />} />
                    
                    {/* Admin (only for admin users) */}
                    {user?.role === 'admin' && (
                      <Route path="/admin" element={<AdminPage />} />
                    )}
                    
                    {/* Catch all */}
                    <Route path="*" element={<Navigate to="/dashboard" replace />} />
                  </Routes>
                </Layout>
              </WebSocketProvider>
            </ProtectedRoute>
          }
        />
      </Routes>
    </div>
  );
}

export default App;
