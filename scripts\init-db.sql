-- NeuroTrade OS Database Initialization Script

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create initial admin user (will be handled by the application)
-- This is just a placeholder for any additional database setup

-- Create indexes for better performance
-- These will be created by Alembic migrations, but we can add any custom ones here

-- Grant permissions
GRANT ALL PRIVILEGES ON DATABASE neurotrade TO neurotrade;

-- Set timezone
SET timezone = 'UTC';
