import React, { useEffect, useState } from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  TimeScale,
} from 'chart.js';
import { Line, Bar } from 'react-chartjs-2';
import 'chartjs-adapter-date-fns';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store/store';
import { fetchDailyStats } from '../../store/slices/tradesSlice';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  TimeScale
);

interface DashboardChartProps {
  type: 'pnl' | 'trades';
}

const DashboardChart: React.FC<DashboardChartProps> = ({ type }) => {
  const dispatch = useDispatch();
  const { dailyStats } = useSelector((state: RootState) => state.trades);
  const [timeRange, setTimeRange] = useState(30);

  useEffect(() => {
    dispatch(fetchDailyStats(timeRange) as any);
  }, [dispatch, timeRange]);

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
        labels: {
          usePointStyle: true,
          padding: 20,
        },
      },
      tooltip: {
        mode: 'index' as const,
        intersect: false,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#fff',
        bodyColor: '#fff',
        borderColor: '#374151',
        borderWidth: 1,
      },
    },
    scales: {
      x: {
        type: 'time' as const,
        time: {
          unit: 'day' as const,
          displayFormats: {
            day: 'MMM dd',
          },
        },
        grid: {
          display: false,
        },
        ticks: {
          maxTicksLimit: 7,
        },
      },
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(156, 163, 175, 0.1)',
        },
        ticks: {
          callback: function(value: any) {
            if (type === 'pnl') {
              return '$' + value.toFixed(2);
            }
            return value;
          },
        },
      },
    },
    interaction: {
      mode: 'nearest' as const,
      axis: 'x' as const,
      intersect: false,
    },
  };

  const generateChartData = () => {
    if (!dailyStats || dailyStats.length === 0) {
      return {
        labels: [],
        datasets: [],
      };
    }

    const labels = dailyStats.map(stat => new Date(stat.date));

    if (type === 'pnl') {
      // Calculate cumulative P&L
      let cumulativePnL = 0;
      const cumulativeData = dailyStats.map(stat => {
        cumulativePnL += stat.profit_loss;
        return cumulativePnL;
      });

      return {
        labels,
        datasets: [
          {
            label: 'Cumulative P&L',
            data: cumulativeData,
            borderColor: 'rgb(59, 130, 246)',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            borderWidth: 2,
            fill: true,
            tension: 0.4,
            pointRadius: 4,
            pointHoverRadius: 6,
            pointBackgroundColor: 'rgb(59, 130, 246)',
            pointBorderColor: '#fff',
            pointBorderWidth: 2,
          },
          {
            label: 'Daily P&L',
            data: dailyStats.map(stat => stat.profit_loss),
            borderColor: dailyStats.map(stat => 
              stat.profit_loss >= 0 ? 'rgb(34, 197, 94)' : 'rgb(239, 68, 68)'
            ),
            backgroundColor: dailyStats.map(stat => 
              stat.profit_loss >= 0 ? 'rgba(34, 197, 94, 0.1)' : 'rgba(239, 68, 68, 0.1)'
            ),
            borderWidth: 2,
            pointRadius: 3,
            pointHoverRadius: 5,
            tension: 0.4,
          },
        ],
      };
    } else {
      return {
        labels,
        datasets: [
          {
            label: 'Total Trades',
            data: dailyStats.map(stat => stat.trades),
            backgroundColor: 'rgba(59, 130, 246, 0.8)',
            borderColor: 'rgb(59, 130, 246)',
            borderWidth: 1,
            borderRadius: 4,
            borderSkipped: false,
          },
        ],
      };
    }
  };

  const chartData = generateChartData();

  return (
    <div className="space-y-4">
      {/* Time Range Selector */}
      <div className="flex items-center justify-between">
        <div className="flex space-x-2">
          {[7, 30, 90].map((days) => (
            <button
              key={days}
              onClick={() => setTimeRange(days)}
              className={`px-3 py-1 text-sm rounded-md transition-colors ${
                timeRange === days
                  ? 'bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300'
                  : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200'
              }`}
            >
              {days}d
            </button>
          ))}
        </div>
        
        {/* Summary Stats */}
        {type === 'pnl' && dailyStats.length > 0 && (
          <div className="text-right">
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Total: 
              <span className={`ml-1 font-medium ${
                dailyStats.reduce((sum, stat) => sum + stat.profit_loss, 0) >= 0
                  ? 'text-success-600 dark:text-success-400'
                  : 'text-danger-600 dark:text-danger-400'
              }`}>
                ${dailyStats.reduce((sum, stat) => sum + stat.profit_loss, 0).toFixed(2)}
              </span>
            </p>
          </div>
        )}
      </div>

      {/* Chart */}
      <div className="h-64">
        {chartData.labels.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <div className="text-gray-400 mb-2">
                <svg className="mx-auto h-12 w-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                No data available for the selected period
              </p>
            </div>
          </div>
        ) : type === 'pnl' ? (
          <Line data={chartData} options={chartOptions} />
        ) : (
          <Bar data={chartData} options={chartOptions} />
        )}
      </div>
    </div>
  );
};

export default DashboardChart;
