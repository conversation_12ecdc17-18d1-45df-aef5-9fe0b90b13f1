"""
Market data service for real-time price feeds
"""
import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Callable
import ccxt
import pandas as pd
from influxdb_client import Point
from influxdb_client.client.write_api import SYNCHRONOUS

from ..core.config import settings
from ..core.database import get_influx

logger = logging.getLogger(__name__)


class MarketDataService:
    """Service for managing market data feeds"""
    
    def __init__(self):
        self.exchanges: Dict[str, ccxt.Exchange] = {}
        self.subscriptions: Dict[str, List[Callable]] = {}
        self.running = False
        self.tasks: List[asyncio.Task] = []
        self.influx_client = get_influx()
        self.write_api = self.influx_client.write_api(write_options=SYNCHRONOUS)
        
    async def start(self):
        """Start the market data service"""
        logger.info("Starting market data service...")
        
        # Initialize exchanges
        await self._initialize_exchanges()
        
        # Start data collection tasks
        self.running = True
        self.tasks = [
            asyncio.create_task(self._price_feed_loop()),
            asyncio.create_task(self._orderbook_feed_loop()),
            asyncio.create_task(self._trades_feed_loop())
        ]
        
        logger.info("Market data service started")
    
    async def stop(self):
        """Stop the market data service"""
        logger.info("Stopping market data service...")
        
        self.running = False
        
        # Cancel all tasks
        for task in self.tasks:
            task.cancel()
        
        # Wait for tasks to complete
        await asyncio.gather(*self.tasks, return_exceptions=True)
        
        # Close exchange connections
        for exchange in self.exchanges.values():
            await exchange.close()
        
        logger.info("Market data service stopped")
    
    async def _initialize_exchanges(self):
        """Initialize exchange connections"""
        exchange_configs = {
            'binance': ccxt.binance({
                'sandbox': settings.ENVIRONMENT != 'production',
                'enableRateLimit': True,
            }),
            'coinbase': ccxt.coinbasepro({
                'sandbox': settings.ENVIRONMENT != 'production',
                'enableRateLimit': True,
            }),
            'kraken': ccxt.kraken({
                'enableRateLimit': True,
            })
        }
        
        for name, exchange in exchange_configs.items():
            try:
                await exchange.load_markets()
                self.exchanges[name] = exchange
                logger.info(f"Initialized {name} exchange")
            except Exception as e:
                logger.error(f"Failed to initialize {name} exchange: {e}")
    
    async def _price_feed_loop(self):
        """Main price feed loop"""
        while self.running:
            try:
                # Get prices for major pairs
                symbols = ['BTC/USDT', 'ETH/USDT', 'BNB/USDT', 'ADA/USDT', 'SOL/USDT']
                
                for exchange_name, exchange in self.exchanges.items():
                    for symbol in symbols:
                        try:
                            ticker = await exchange.fetch_ticker(symbol)
                            await self._store_price_data(exchange_name, symbol, ticker)
                            await self._notify_subscribers(f"price:{exchange_name}:{symbol}", ticker)
                        except Exception as e:
                            logger.error(f"Error fetching {symbol} from {exchange_name}: {e}")
                
                await asyncio.sleep(1)  # Update every second
                
            except Exception as e:
                logger.error(f"Error in price feed loop: {e}")
                await asyncio.sleep(5)
    
    async def _orderbook_feed_loop(self):
        """Order book feed loop"""
        while self.running:
            try:
                symbols = ['BTC/USDT', 'ETH/USDT']
                
                for exchange_name, exchange in self.exchanges.items():
                    for symbol in symbols:
                        try:
                            orderbook = await exchange.fetch_order_book(symbol, limit=20)
                            await self._store_orderbook_data(exchange_name, symbol, orderbook)
                            await self._notify_subscribers(f"orderbook:{exchange_name}:{symbol}", orderbook)
                        except Exception as e:
                            logger.error(f"Error fetching orderbook {symbol} from {exchange_name}: {e}")
                
                await asyncio.sleep(2)  # Update every 2 seconds
                
            except Exception as e:
                logger.error(f"Error in orderbook feed loop: {e}")
                await asyncio.sleep(5)
    
    async def _trades_feed_loop(self):
        """Recent trades feed loop"""
        while self.running:
            try:
                symbols = ['BTC/USDT', 'ETH/USDT']
                
                for exchange_name, exchange in self.exchanges.items():
                    for symbol in symbols:
                        try:
                            trades = await exchange.fetch_trades(symbol, limit=50)
                            await self._store_trades_data(exchange_name, symbol, trades)
                            await self._notify_subscribers(f"trades:{exchange_name}:{symbol}", trades)
                        except Exception as e:
                            logger.error(f"Error fetching trades {symbol} from {exchange_name}: {e}")
                
                await asyncio.sleep(5)  # Update every 5 seconds
                
            except Exception as e:
                logger.error(f"Error in trades feed loop: {e}")
                await asyncio.sleep(10)
    
    async def _store_price_data(self, exchange: str, symbol: str, ticker: dict):
        """Store price data in InfluxDB"""
        try:
            point = Point("prices") \
                .tag("exchange", exchange) \
                .tag("symbol", symbol) \
                .field("price", float(ticker['last'])) \
                .field("bid", float(ticker['bid']) if ticker['bid'] else 0) \
                .field("ask", float(ticker['ask']) if ticker['ask'] else 0) \
                .field("volume", float(ticker['baseVolume']) if ticker['baseVolume'] else 0) \
                .field("high", float(ticker['high']) if ticker['high'] else 0) \
                .field("low", float(ticker['low']) if ticker['low'] else 0) \
                .time(datetime.utcnow())
            
            self.write_api.write(bucket=settings.INFLUXDB_BUCKET, record=point)
            
        except Exception as e:
            logger.error(f"Error storing price data: {e}")
    
    async def _store_orderbook_data(self, exchange: str, symbol: str, orderbook: dict):
        """Store orderbook data in InfluxDB"""
        try:
            # Store top 5 bids and asks
            for i, (price, amount) in enumerate(orderbook['bids'][:5]):
                point = Point("orderbook") \
                    .tag("exchange", exchange) \
                    .tag("symbol", symbol) \
                    .tag("side", "bid") \
                    .tag("level", str(i)) \
                    .field("price", float(price)) \
                    .field("amount", float(amount)) \
                    .time(datetime.utcnow())
                
                self.write_api.write(bucket=settings.INFLUXDB_BUCKET, record=point)
            
            for i, (price, amount) in enumerate(orderbook['asks'][:5]):
                point = Point("orderbook") \
                    .tag("exchange", exchange) \
                    .tag("symbol", symbol) \
                    .tag("side", "ask") \
                    .tag("level", str(i)) \
                    .field("price", float(price)) \
                    .field("amount", float(amount)) \
                    .time(datetime.utcnow())
                
                self.write_api.write(bucket=settings.INFLUXDB_BUCKET, record=point)
                
        except Exception as e:
            logger.error(f"Error storing orderbook data: {e}")
    
    async def _store_trades_data(self, exchange: str, symbol: str, trades: list):
        """Store trades data in InfluxDB"""
        try:
            for trade in trades[-10:]:  # Store last 10 trades
                point = Point("trades") \
                    .tag("exchange", exchange) \
                    .tag("symbol", symbol) \
                    .tag("side", trade['side']) \
                    .field("price", float(trade['price'])) \
                    .field("amount", float(trade['amount'])) \
                    .field("cost", float(trade['cost'])) \
                    .time(datetime.fromtimestamp(trade['timestamp'] / 1000))
                
                self.write_api.write(bucket=settings.INFLUXDB_BUCKET, record=point)
                
        except Exception as e:
            logger.error(f"Error storing trades data: {e}")
    
    async def _notify_subscribers(self, channel: str, data: dict):
        """Notify subscribers of new data"""
        if channel in self.subscriptions:
            for callback in self.subscriptions[channel]:
                try:
                    await callback(data)
                except Exception as e:
                    logger.error(f"Error notifying subscriber: {e}")
    
    def subscribe(self, channel: str, callback: Callable):
        """Subscribe to market data updates"""
        if channel not in self.subscriptions:
            self.subscriptions[channel] = []
        self.subscriptions[channel].append(callback)
    
    def unsubscribe(self, channel: str, callback: Callable):
        """Unsubscribe from market data updates"""
        if channel in self.subscriptions:
            try:
                self.subscriptions[channel].remove(callback)
            except ValueError:
                pass
    
    async def get_current_price(self, exchange: str, symbol: str) -> Optional[float]:
        """Get current price for a symbol"""
        if exchange in self.exchanges:
            try:
                ticker = await self.exchanges[exchange].fetch_ticker(symbol)
                return float(ticker['last'])
            except Exception as e:
                logger.error(f"Error getting current price: {e}")
        return None
    
    async def get_historical_data(
        self,
        exchange: str,
        symbol: str,
        timeframe: str = '1h',
        limit: int = 100
    ) -> Optional[pd.DataFrame]:
        """Get historical OHLCV data"""
        if exchange in self.exchanges:
            try:
                ohlcv = await self.exchanges[exchange].fetch_ohlcv(
                    symbol, timeframe, limit=limit
                )
                df = pd.DataFrame(
                    ohlcv,
                    columns=['timestamp', 'open', 'high', 'low', 'close', 'volume']
                )
                df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                return df
            except Exception as e:
                logger.error(f"Error getting historical data: {e}")
        return None
