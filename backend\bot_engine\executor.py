"""
Trade execution engine
"""
import logging
from datetime import datetime
from typing import Optional, Dict, Any
import ccxt
from sqlalchemy.orm import Session

from ..core.database import SessionLocal
from ..core.security import APIKeyManager
from ..models.trading import TradingBot, Trade, TradeType, TradeStatus, OrderType
from ..models.user import ExchangeAPIKey
from ..bot_engine.strategies import TradingSignal

logger = logging.getLogger(__name__)


class TradeExecutor:
    """Handles trade execution for bots"""
    
    def __init__(self, bot: TradingBot):
        self.bot = bot
        self.exchange_client: Optional[ccxt.Exchange] = None
        
    async def execute_trade(self, signal: TradingSignal) -> Optional[Trade]:
        """Execute a trade based on a signal"""
        try:
            # Initialize exchange client if needed
            if not self.exchange_client:
                await self._initialize_exchange()
            
            if not self.exchange_client:
                logger.error(f"No exchange client available for bot {self.bot.id}")
                return None
            
            # Calculate position size
            position_size = self._calculate_position_size(signal)
            if position_size <= 0:
                logger.warning(f"Invalid position size for bot {self.bot.id}")
                return None
            
            # Create trade record
            trade = await self._create_trade_record(signal, position_size)
            
            # Execute the trade
            if signal.signal_type == TradeType.BUY:
                result = await self._execute_buy_order(trade)
            else:
                result = await self._execute_sell_order(trade)
            
            # Update trade record with execution result
            await self._update_trade_record(trade, result)
            
            # Update bot statistics
            await self._update_bot_statistics(trade)
            
            return trade
            
        except Exception as e:
            logger.error(f"Error executing trade for bot {self.bot.id}: {e}")
            return None
    
    async def _initialize_exchange(self):
        """Initialize exchange client with user's API keys"""
        db = SessionLocal()
        try:
            # Get user's API keys for this exchange
            api_key_record = db.query(ExchangeAPIKey).filter(
                ExchangeAPIKey.user_id == self.bot.user_id,
                ExchangeAPIKey.exchange_name == self.bot.exchange,
                ExchangeAPIKey.is_active == True
            ).first()
            
            if not api_key_record:
                logger.error(f"No API keys found for exchange {self.bot.exchange}")
                return
            
            # Decrypt API keys
            api_key = APIKeyManager.decrypt_key(api_key_record.api_key)
            secret_key = APIKeyManager.decrypt_key(api_key_record.secret_key)
            passphrase = None
            if api_key_record.passphrase:
                passphrase = APIKeyManager.decrypt_key(api_key_record.passphrase)
            
            # Initialize exchange client
            exchange_config = {
                'apiKey': api_key,
                'secret': secret_key,
                'sandbox': api_key_record.is_testnet,
                'enableRateLimit': True,
            }
            
            if passphrase:
                exchange_config['password'] = passphrase
            
            if self.bot.exchange.lower() == 'binance':
                self.exchange_client = ccxt.binance(exchange_config)
            elif self.bot.exchange.lower() == 'coinbase':
                self.exchange_client = ccxt.coinbasepro(exchange_config)
            elif self.bot.exchange.lower() == 'kraken':
                self.exchange_client = ccxt.kraken(exchange_config)
            else:
                logger.error(f"Unsupported exchange: {self.bot.exchange}")
                return
            
            # Load markets
            await self.exchange_client.load_markets()
            
        except Exception as e:
            logger.error(f"Error initializing exchange client: {e}")
        finally:
            db.close()
    
    def _calculate_position_size(self, signal: TradingSignal) -> float:
        """Calculate position size based on bot configuration"""
        try:
            # Use fixed position size from bot configuration
            base_size = self.bot.position_size
            
            # Adjust based on signal strength
            strength_multiplier = signal.strength / 100.0
            
            # Apply risk management
            risk_adjusted_size = base_size * strength_multiplier
            
            # Ensure minimum and maximum limits
            min_size = 10.0  # Minimum $10 trade
            max_size = self.bot.position_size * 2  # Maximum 2x base size
            
            return max(min_size, min(max_size, risk_adjusted_size))
            
        except Exception as e:
            logger.error(f"Error calculating position size: {e}")
            return 0
    
    async def _create_trade_record(self, signal: TradingSignal, position_size: float) -> Trade:
        """Create trade record in database"""
        db = SessionLocal()
        try:
            # Calculate quantity based on current price
            if signal.signal_type == TradeType.BUY:
                quantity = position_size / signal.price
            else:
                # For sell orders, we need to check available balance
                # For now, use the same calculation
                quantity = position_size / signal.price
            
            trade = Trade(
                user_id=self.bot.user_id,
                bot_id=self.bot.id,
                symbol=self.bot.symbol,
                exchange=self.bot.exchange,
                trade_type=signal.signal_type,
                order_type=OrderType.MARKET,  # Default to market orders
                quantity=quantity,
                price=signal.price,
                total_value=position_size,
                status=TradeStatus.PENDING
            )
            
            db.add(trade)
            db.commit()
            db.refresh(trade)
            
            return trade
            
        except Exception as e:
            logger.error(f"Error creating trade record: {e}")
            db.rollback()
            raise
        finally:
            db.close()
    
    async def _execute_buy_order(self, trade: Trade) -> Dict[str, Any]:
        """Execute buy order"""
        try:
            # Place market buy order
            order = await self.exchange_client.create_market_buy_order(
                symbol=trade.symbol,
                amount=trade.quantity
            )
            
            return {
                'success': True,
                'order_id': order['id'],
                'filled_quantity': order.get('filled', 0),
                'filled_price': order.get('average', trade.price),
                'fees': order.get('fee', {}).get('cost', 0),
                'order_data': order
            }
            
        except Exception as e:
            logger.error(f"Error executing buy order: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def _execute_sell_order(self, trade: Trade) -> Dict[str, Any]:
        """Execute sell order"""
        try:
            # Place market sell order
            order = await self.exchange_client.create_market_sell_order(
                symbol=trade.symbol,
                amount=trade.quantity
            )
            
            return {
                'success': True,
                'order_id': order['id'],
                'filled_quantity': order.get('filled', 0),
                'filled_price': order.get('average', trade.price),
                'fees': order.get('fee', {}).get('cost', 0),
                'order_data': order
            }
            
        except Exception as e:
            logger.error(f"Error executing sell order: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def _update_trade_record(self, trade: Trade, execution_result: Dict[str, Any]):
        """Update trade record with execution results"""
        db = SessionLocal()
        try:
            db_trade = db.query(Trade).filter(Trade.id == trade.id).first()
            if not db_trade:
                return
            
            if execution_result['success']:
                db_trade.status = TradeStatus.FILLED
                db_trade.exchange_order_id = execution_result['order_id']
                db_trade.filled_quantity = execution_result['filled_quantity']
                db_trade.filled_price = execution_result['filled_price']
                db_trade.fees = execution_result['fees']
                db_trade.executed_at = datetime.utcnow()
                
                # Calculate profit/loss (simplified)
                if db_trade.trade_type == TradeType.SELL:
                    # For sell orders, calculate P&L based on average buy price
                    # This is simplified - in reality, you'd track the cost basis
                    db_trade.profit_loss = (db_trade.filled_price - db_trade.price) * db_trade.filled_quantity
                
            else:
                db_trade.status = TradeStatus.FAILED
            
            db.commit()
            
        except Exception as e:
            logger.error(f"Error updating trade record: {e}")
            db.rollback()
        finally:
            db.close()
    
    async def _update_bot_statistics(self, trade: Trade):
        """Update bot performance statistics"""
        db = SessionLocal()
        try:
            bot = db.query(TradingBot).filter(TradingBot.id == self.bot.id).first()
            if not bot:
                return
            
            # Update trade counts
            bot.total_trades += 1
            
            if trade.status == TradeStatus.FILLED:
                if trade.profit_loss and trade.profit_loss > 0:
                    bot.winning_trades += 1
                elif trade.profit_loss and trade.profit_loss < 0:
                    bot.losing_trades += 1
                
                # Update total P&L
                if trade.profit_loss:
                    bot.total_profit_loss += trade.profit_loss
            
            db.commit()
            
        except Exception as e:
            logger.error(f"Error updating bot statistics: {e}")
            db.rollback()
        finally:
            db.close()
    
    async def check_order_status(self, trade: Trade) -> Optional[Dict[str, Any]]:
        """Check the status of an order"""
        if not self.exchange_client or not trade.exchange_order_id:
            return None
        
        try:
            order = await self.exchange_client.fetch_order(
                trade.exchange_order_id,
                trade.symbol
            )
            return order
        except Exception as e:
            logger.error(f"Error checking order status: {e}")
            return None
    
    async def cancel_order(self, trade: Trade) -> bool:
        """Cancel an open order"""
        if not self.exchange_client or not trade.exchange_order_id:
            return False
        
        try:
            await self.exchange_client.cancel_order(
                trade.exchange_order_id,
                trade.symbol
            )
            
            # Update trade status
            db = SessionLocal()
            try:
                db_trade = db.query(Trade).filter(Trade.id == trade.id).first()
                if db_trade:
                    db_trade.status = TradeStatus.CANCELLED
                    db.commit()
            finally:
                db.close()
            
            return True
            
        except Exception as e:
            logger.error(f"Error cancelling order: {e}")
            return False
