import React, { createContext, useContext, useEffect, useState, useRef } from 'react';
import { useSelector } from 'react-redux';
import { io, Socket } from 'socket.io-client';
import { RootState } from '../store/store';
import toast from 'react-hot-toast';

interface WebSocketContextType {
  socket: Socket | null;
  isConnected: boolean;
  subscribe: (channel: string) => void;
  unsubscribe: (channel: string) => void;
  sendMessage: (message: any) => void;
}

const WebSocketContext = createContext<WebSocketContextType | undefined>(undefined);

export const useWebSocket = () => {
  const context = useContext(WebSocketContext);
  if (!context) {
    throw new Error('useWebSocket must be used within a WebSocketProvider');
  }
  return context;
};

interface WebSocketProviderProps {
  children: React.ReactNode;
}

export const WebSocketProvider: React.FC<WebSocketProviderProps> = ({ children }) => {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const { token, isAuthenticated } = useSelector((state: RootState) => state.auth);
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 5;

  useEffect(() => {
    if (!isAuthenticated || !token) {
      if (socket) {
        socket.disconnect();
        setSocket(null);
        setIsConnected(false);
      }
      return;
    }

    const connectWebSocket = () => {
      const wsUrl = process.env.REACT_APP_WS_URL || 'ws://localhost:8000';
      
      const newSocket = io(wsUrl, {
        auth: {
          token: token,
        },
        transports: ['websocket'],
        upgrade: false,
      });

      newSocket.on('connect', () => {
        console.log('WebSocket connected');
        setIsConnected(true);
        reconnectAttempts.current = 0;
        toast.success('Connected to real-time updates');
      });

      newSocket.on('disconnect', (reason) => {
        console.log('WebSocket disconnected:', reason);
        setIsConnected(false);
        
        if (reason === 'io server disconnect') {
          // Server disconnected, try to reconnect
          if (reconnectAttempts.current < maxReconnectAttempts) {
            reconnectAttempts.current++;
            setTimeout(() => {
              newSocket.connect();
            }, 1000 * reconnectAttempts.current);
          } else {
            toast.error('Connection lost. Please refresh the page.');
          }
        }
      });

      newSocket.on('connect_error', (error) => {
        console.error('WebSocket connection error:', error);
        setIsConnected(false);
        toast.error('Failed to connect to real-time updates');
      });

      // Handle incoming messages
      newSocket.on('price_update', (data) => {
        // Handle price updates
        console.log('Price update:', data);
      });

      newSocket.on('bot_update', (data) => {
        // Handle bot updates
        console.log('Bot update:', data);
        toast.success(`Bot ${data.data.name} status updated`);
      });

      newSocket.on('trade_notification', (data) => {
        // Handle trade notifications
        console.log('Trade notification:', data);
        toast.success(`Trade executed: ${data.data.symbol}`);
      });

      newSocket.on('system_notification', (data) => {
        // Handle system notifications
        console.log('System notification:', data);
        toast.info(data.data.message);
      });

      newSocket.on('error', (data) => {
        console.error('WebSocket error:', data);
        toast.error(data.message || 'WebSocket error occurred');
      });

      setSocket(newSocket);
    };

    connectWebSocket();

    return () => {
      if (socket) {
        socket.disconnect();
      }
    };
  }, [isAuthenticated, token]);

  const subscribe = (channel: string) => {
    if (socket && isConnected) {
      socket.emit('subscribe', { channels: [channel] });
    }
  };

  const unsubscribe = (channel: string) => {
    if (socket && isConnected) {
      socket.emit('unsubscribe', { channels: [channel] });
    }
  };

  const sendMessage = (message: any) => {
    if (socket && isConnected) {
      socket.emit('message', message);
    }
  };

  const value: WebSocketContextType = {
    socket,
    isConnected,
    subscribe,
    unsubscribe,
    sendMessage,
  };

  return (
    <WebSocketContext.Provider value={value}>
      {children}
    </WebSocketContext.Provider>
  );
};
