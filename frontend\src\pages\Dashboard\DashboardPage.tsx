import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link } from 'react-router-dom';
import {
  CpuChipIcon,
  ChartBarIcon,
  CurrencyDollarIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  PlayIcon,
  PauseIcon,
} from '@heroicons/react/24/outline';
import { RootState } from '../../store/store';
import { fetchBots } from '../../store/slices/botsSlice';
import { fetchTrades, fetchTradeStats } from '../../store/slices/tradesSlice';
import LoadingSpinner from '../../components/UI/LoadingSpinner';
import DashboardChart from '../../components/Dashboard/DashboardChart';
import { useWebSocket } from '../../contexts/WebSocketContext';

const DashboardPage: React.FC = () => {
  const dispatch = useDispatch();
  const { user } = useSelector((state: RootState) => state.auth);
  const { bots } = useSelector((state: RootState) => state.bots);
  const { trades, stats } = useSelector((state: RootState) => state.trades);
  const { isConnected } = useWebSocket();

  useEffect(() => {
    // Fetch initial data
    dispatch(fetchBots() as any);
    dispatch(fetchTrades({ limit: 10 }) as any);
    dispatch(fetchTradeStats() as any);
  }, [dispatch]);

  // Calculate dashboard stats
  const runningBots = bots.filter(bot => bot.status === 'running').length;
  const totalBots = bots.length;
  const totalPnL = stats?.total_profit_loss || 0;
  const winRate = stats?.win_rate || 0;

  const dashboardStats = [
    {
      name: 'Total Bots',
      value: totalBots,
      change: `${runningBots} running`,
      changeType: runningBots > 0 ? 'positive' : 'neutral',
      icon: CpuChipIcon,
    },
    {
      name: 'Total Trades',
      value: stats?.total_trades || 0,
      change: `${stats?.winning_trades || 0} winning`,
      changeType: (stats?.winning_trades || 0) > (stats?.losing_trades || 0) ? 'positive' : 'negative',
      icon: ChartBarIcon,
    },
    {
      name: 'Total P&L',
      value: `$${totalPnL.toFixed(2)}`,
      change: `${winRate.toFixed(1)}% win rate`,
      changeType: totalPnL >= 0 ? 'positive' : 'negative',
      icon: CurrencyDollarIcon,
    },
    {
      name: 'Win Rate',
      value: `${winRate.toFixed(1)}%`,
      change: `${stats?.total_trades || 0} total trades`,
      changeType: winRate >= 50 ? 'positive' : 'negative',
      icon: TrendingUpIcon,
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            Welcome back, {user?.username}!
          </h1>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Here's what's happening with your trading bots today.
          </p>
        </div>
        <div className="flex items-center space-x-3">
          {/* Connection Status */}
          <div className="flex items-center space-x-2">
            <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-success-500' : 'bg-danger-500'}`} />
            <span className="text-sm text-gray-500 dark:text-gray-400">
              {isConnected ? 'Connected' : 'Disconnected'}
            </span>
          </div>
          
          {/* Quick Actions */}
          <Link
            to="/bots/create"
            className="btn-primary"
          >
            Create Bot
          </Link>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {dashboardStats.map((stat) => (
          <div key={stat.name} className="card">
            <div className="card-body">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <stat.icon className="h-6 w-6 text-gray-400" aria-hidden="true" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                      {stat.name}
                    </dt>
                    <dd className="flex items-baseline">
                      <div className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                        {stat.value}
                      </div>
                      <div className={`ml-2 flex items-baseline text-sm font-semibold ${
                        stat.changeType === 'positive' 
                          ? 'text-success-600 dark:text-success-400' 
                          : stat.changeType === 'negative'
                          ? 'text-danger-600 dark:text-danger-400'
                          : 'text-gray-500 dark:text-gray-400'
                      }`}>
                        {stat.changeType === 'positive' && <TrendingUpIcon className="h-4 w-4 mr-1" />}
                        {stat.changeType === 'negative' && <TrendingDownIcon className="h-4 w-4 mr-1" />}
                        {stat.change}
                      </div>
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* P&L Chart */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
              Profit & Loss
            </h3>
          </div>
          <div className="card-body">
            <DashboardChart type="pnl" />
          </div>
        </div>

        {/* Trades Chart */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
              Daily Trades
            </h3>
          </div>
          <div className="card-body">
            <DashboardChart type="trades" />
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Active Bots */}
        <div className="card">
          <div className="card-header flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
              Active Bots
            </h3>
            <Link
              to="/bots"
              className="text-sm text-primary-600 hover:text-primary-500 dark:text-primary-400"
            >
              View all
            </Link>
          </div>
          <div className="card-body">
            {bots.length === 0 ? (
              <div className="text-center py-6">
                <CpuChipIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">
                  No bots yet
                </h3>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  Get started by creating your first trading bot.
                </p>
                <div className="mt-6">
                  <Link to="/bots/create" className="btn-primary">
                    Create Bot
                  </Link>
                </div>
              </div>
            ) : (
              <div className="space-y-3">
                {bots.slice(0, 5).map((bot) => (
                  <div key={bot.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-dark-700 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className={`w-2 h-2 rounded-full ${
                        bot.status === 'running' ? 'bg-success-500' :
                        bot.status === 'paused' ? 'bg-warning-500' :
                        'bg-gray-400'
                      }`} />
                      <div>
                        <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          {bot.name}
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          {bot.symbol} • {bot.strategy}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className={`text-sm font-medium ${
                        bot.total_profit_loss >= 0 
                          ? 'text-success-600 dark:text-success-400'
                          : 'text-danger-600 dark:text-danger-400'
                      }`}>
                        ${bot.total_profit_loss.toFixed(2)}
                      </span>
                      {bot.status === 'running' ? (
                        <PlayIcon className="h-4 w-4 text-success-500" />
                      ) : (
                        <PauseIcon className="h-4 w-4 text-gray-400" />
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Recent Trades */}
        <div className="card">
          <div className="card-header flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
              Recent Trades
            </h3>
            <Link
              to="/trades"
              className="text-sm text-primary-600 hover:text-primary-500 dark:text-primary-400"
            >
              View all
            </Link>
          </div>
          <div className="card-body">
            {trades.length === 0 ? (
              <div className="text-center py-6">
                <ChartBarIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">
                  No trades yet
                </h3>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  Trades will appear here once your bots start trading.
                </p>
              </div>
            ) : (
              <div className="space-y-3">
                {trades.slice(0, 5).map((trade) => (
                  <div key={trade.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-dark-700 rounded-lg">
                    <div>
                      <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {trade.trade_type.toUpperCase()} {trade.symbol}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {new Date(trade.created_at).toLocaleString()}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className={`text-sm font-medium ${
                        trade.profit_loss >= 0 
                          ? 'text-success-600 dark:text-success-400'
                          : 'text-danger-600 dark:text-danger-400'
                      }`}>
                        ${trade.profit_loss.toFixed(2)}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {trade.status}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardPage;
