#!/usr/bin/env python3
"""
Create initial admin user for NeuroTrade OS
"""
import asyncio
import sys
import os
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

from core.database import SessionLocal
from core.security import get_password_hash
from models.user import User, UserRole, UserStatus


async def create_admin_user():
    """Create initial admin user"""
    db = SessionLocal()
    
    try:
        # Check if admin user already exists
        existing_admin = db.query(User).filter(
            User.email == "<EMAIL>"
        ).first()
        
        if existing_admin:
            print("Admin user already exists!")
            return
        
        # Create admin user
        admin_user = User(
            email="<EMAIL>",
            username="admin",
            hashed_password=get_password_hash("admin_secure_password"),
            first_name="System",
            last_name="Administrator",
            role=UserRole.ADMIN,
            status=UserStatus.ACTIVE,
            is_email_verified=True,
            max_bots=100,
            max_concurrent_trades=1000,
            risk_limit_percentage=100.0
        )
        
        db.add(admin_user)
        
        # Create trader user for demo
        trader_user = User(
            email="<EMAIL>",
            username="trader",
            hashed_password=get_password_hash("trader_password"),
            first_name="Demo",
            last_name="Trader",
            role=UserRole.TRADER,
            status=UserStatus.ACTIVE,
            is_email_verified=True,
            max_bots=10,
            max_concurrent_trades=50,
            risk_limit_percentage=10.0
        )
        
        db.add(trader_user)
        
        # Create viewer user for demo
        viewer_user = User(
            email="<EMAIL>",
            username="viewer",
            hashed_password=get_password_hash("viewer_password"),
            first_name="Demo",
            last_name="Viewer",
            role=UserRole.VIEWER,
            status=UserStatus.ACTIVE,
            is_email_verified=True,
            max_bots=0,
            max_concurrent_trades=0,
            risk_limit_percentage=0.0
        )
        
        db.add(viewer_user)
        
        db.commit()
        
        print("✅ Successfully created initial users:")
        print("   Admin: <EMAIL> / admin_secure_password")
        print("   Trader: <EMAIL> / trader_password")
        print("   Viewer: <EMAIL> / viewer_password")
        
    except Exception as e:
        print(f"❌ Error creating admin user: {e}")
        db.rollback()
    finally:
        db.close()


if __name__ == "__main__":
    asyncio.run(create_admin_user())
