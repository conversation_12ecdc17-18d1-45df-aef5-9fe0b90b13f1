import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { botsAPI } from '../../services/api';

export interface Bot {
  id: number;
  name: string;
  description?: string;
  strategy: string;
  symbol: string;
  exchange: string;
  status: string;
  is_testnet: boolean;
  position_size: number;
  risk_percentage: number;
  max_drawdown: number;
  stop_loss_percentage: number;
  take_profit_percentage: number;
  execution_interval: number;
  total_trades: number;
  winning_trades: number;
  losing_trades: number;
  total_profit_loss: number;
  current_drawdown: number;
  created_at: string;
  started_at?: string;
  last_execution?: string;
}

export interface BotSignal {
  id: number;
  signal_type: string;
  strength: number;
  confidence: number;
  price: number;
  indicators: any;
  reason: string;
  action_taken: boolean;
  created_at: string;
}

export interface Strategy {
  name: string;
  description: string;
  parameters: any;
}

interface BotsState {
  bots: Bot[];
  currentBot: Bot | null;
  botSignals: BotSignal[];
  availableStrategies: { [key: string]: Strategy };
  isLoading: boolean;
  error: string | null;
}

const initialState: BotsState = {
  bots: [],
  currentBot: null,
  botSignals: [],
  availableStrategies: {},
  isLoading: false,
  error: null,
};

// Async thunks
export const fetchBots = createAsyncThunk(
  'bots/fetchBots',
  async (_, { rejectWithValue }) => {
    try {
      const response = await botsAPI.getBots();
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.detail || 'Failed to fetch bots');
    }
  }
);

export const fetchBot = createAsyncThunk(
  'bots/fetchBot',
  async (id: number, { rejectWithValue }) => {
    try {
      const response = await botsAPI.getBot(id);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.detail || 'Failed to fetch bot');
    }
  }
);

export const createBot = createAsyncThunk(
  'bots/createBot',
  async (botData: any, { rejectWithValue }) => {
    try {
      const response = await botsAPI.createBot(botData);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.detail || 'Failed to create bot');
    }
  }
);

export const updateBot = createAsyncThunk(
  'bots/updateBot',
  async ({ id, botData }: { id: number; botData: any }, { rejectWithValue }) => {
    try {
      const response = await botsAPI.updateBot(id, botData);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.detail || 'Failed to update bot');
    }
  }
);

export const deleteBot = createAsyncThunk(
  'bots/deleteBot',
  async (id: number, { rejectWithValue }) => {
    try {
      await botsAPI.deleteBot(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.detail || 'Failed to delete bot');
    }
  }
);

export const startBot = createAsyncThunk(
  'bots/startBot',
  async (id: number, { rejectWithValue }) => {
    try {
      await botsAPI.startBot(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.detail || 'Failed to start bot');
    }
  }
);

export const stopBot = createAsyncThunk(
  'bots/stopBot',
  async (id: number, { rejectWithValue }) => {
    try {
      await botsAPI.stopBot(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.detail || 'Failed to stop bot');
    }
  }
);

export const pauseBot = createAsyncThunk(
  'bots/pauseBot',
  async (id: number, { rejectWithValue }) => {
    try {
      await botsAPI.pauseBot(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.detail || 'Failed to pause bot');
    }
  }
);

export const resumeBot = createAsyncThunk(
  'bots/resumeBot',
  async (id: number, { rejectWithValue }) => {
    try {
      await botsAPI.resumeBot(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.detail || 'Failed to resume bot');
    }
  }
);

export const fetchBotSignals = createAsyncThunk(
  'bots/fetchBotSignals',
  async ({ id, limit = 50 }: { id: number; limit?: number }, { rejectWithValue }) => {
    try {
      const response = await botsAPI.getBotSignals(id, limit);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.detail || 'Failed to fetch bot signals');
    }
  }
);

export const fetchAvailableStrategies = createAsyncThunk(
  'bots/fetchAvailableStrategies',
  async (_, { rejectWithValue }) => {
    try {
      const response = await botsAPI.getAvailableStrategies();
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.detail || 'Failed to fetch strategies');
    }
  }
);

const botsSlice = createSlice({
  name: 'bots',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    clearCurrentBot: (state) => {
      state.currentBot = null;
    },
    updateBotStatus: (state, action: PayloadAction<{ id: number; status: string }>) => {
      const bot = state.bots.find(b => b.id === action.payload.id);
      if (bot) {
        bot.status = action.payload.status;
      }
      if (state.currentBot && state.currentBot.id === action.payload.id) {
        state.currentBot.status = action.payload.status;
      }
    },
  },
  extraReducers: (builder) => {
    // Fetch Bots
    builder
      .addCase(fetchBots.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchBots.fulfilled, (state, action) => {
        state.isLoading = false;
        state.bots = action.payload;
        state.error = null;
      })
      .addCase(fetchBots.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Fetch Bot
    builder
      .addCase(fetchBot.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchBot.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentBot = action.payload;
        state.error = null;
      })
      .addCase(fetchBot.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Create Bot
    builder
      .addCase(createBot.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createBot.fulfilled, (state, action) => {
        state.isLoading = false;
        state.bots.push(action.payload);
        state.error = null;
      })
      .addCase(createBot.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Update Bot
    builder
      .addCase(updateBot.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateBot.fulfilled, (state, action) => {
        state.isLoading = false;
        const index = state.bots.findIndex(bot => bot.id === action.payload.id);
        if (index !== -1) {
          state.bots[index] = action.payload;
        }
        if (state.currentBot && state.currentBot.id === action.payload.id) {
          state.currentBot = action.payload;
        }
        state.error = null;
      })
      .addCase(updateBot.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Delete Bot
    builder
      .addCase(deleteBot.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deleteBot.fulfilled, (state, action) => {
        state.isLoading = false;
        state.bots = state.bots.filter(bot => bot.id !== action.payload);
        if (state.currentBot && state.currentBot.id === action.payload) {
          state.currentBot = null;
        }
        state.error = null;
      })
      .addCase(deleteBot.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Start Bot
    builder
      .addCase(startBot.fulfilled, (state, action) => {
        const bot = state.bots.find(b => b.id === action.payload);
        if (bot) {
          bot.status = 'running';
        }
        if (state.currentBot && state.currentBot.id === action.payload) {
          state.currentBot.status = 'running';
        }
      });

    // Stop Bot
    builder
      .addCase(stopBot.fulfilled, (state, action) => {
        const bot = state.bots.find(b => b.id === action.payload);
        if (bot) {
          bot.status = 'stopped';
        }
        if (state.currentBot && state.currentBot.id === action.payload) {
          state.currentBot.status = 'stopped';
        }
      });

    // Pause Bot
    builder
      .addCase(pauseBot.fulfilled, (state, action) => {
        const bot = state.bots.find(b => b.id === action.payload);
        if (bot) {
          bot.status = 'paused';
        }
        if (state.currentBot && state.currentBot.id === action.payload) {
          state.currentBot.status = 'paused';
        }
      });

    // Resume Bot
    builder
      .addCase(resumeBot.fulfilled, (state, action) => {
        const bot = state.bots.find(b => b.id === action.payload);
        if (bot) {
          bot.status = 'running';
        }
        if (state.currentBot && state.currentBot.id === action.payload) {
          state.currentBot.status = 'running';
        }
      });

    // Fetch Bot Signals
    builder
      .addCase(fetchBotSignals.fulfilled, (state, action) => {
        state.botSignals = action.payload;
      });

    // Fetch Available Strategies
    builder
      .addCase(fetchAvailableStrategies.fulfilled, (state, action) => {
        state.availableStrategies = action.payload.strategy_info;
      });
  },
});

export const { clearError, clearCurrentBot, updateBotStatus } = botsSlice.actions;
export default botsSlice.reducer;
