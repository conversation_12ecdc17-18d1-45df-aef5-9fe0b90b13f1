"""
Admin API endpoints
"""
from typing import List, Optional
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, and_
from pydantic import BaseModel

from ..core.database import get_db
from ..api.auth import get_current_admin_user
from ..models.user import User, UserRole, UserStatus, AuditLog
from ..models.trading import TradingBot, Trade, BotStatus, TradeStatus
from ..services.audit import AuditService

router = APIRouter()


# Pydantic models
class AdminUserResponse(BaseModel):
    id: int
    email: str
    username: str
    first_name: Optional[str]
    last_name: Optional[str]
    role: str
    status: str
    is_2fa_enabled: bool
    is_email_verified: bool
    max_bots: int
    max_concurrent_trades: int
    risk_limit_percentage: float
    created_at: str
    last_login: Optional[str]
    bot_count: int
    trade_count: int


class AdminStatsResponse(BaseModel):
    total_users: int
    active_users: int
    total_bots: int
    running_bots: int
    total_trades_24h: int
    successful_trades_24h: int
    total_volume_24h: float
    system_uptime: str


class UserUpdateRequest(BaseModel):
    role: Optional[UserRole] = None
    status: Optional[UserStatus] = None
    max_bots: Optional[int] = None
    max_concurrent_trades: Optional[int] = None
    risk_limit_percentage: Optional[float] = None


@router.get("/stats", response_model=AdminStatsResponse)
async def get_admin_stats(
    admin_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """Get admin dashboard statistics"""
    
    # User statistics
    total_users = db.query(User).count()
    active_users = db.query(User).filter(User.status == UserStatus.ACTIVE).count()
    
    # Bot statistics
    total_bots = db.query(TradingBot).count()
    running_bots = db.query(TradingBot).filter(TradingBot.status == BotStatus.RUNNING).count()
    
    # Trade statistics (last 24 hours)
    yesterday = datetime.utcnow() - timedelta(days=1)
    trades_24h = db.query(Trade).filter(Trade.created_at >= yesterday).all()
    
    total_trades_24h = len(trades_24h)
    successful_trades_24h = len([t for t in trades_24h if t.status == TradeStatus.FILLED])
    total_volume_24h = sum(t.total_value for t in trades_24h if t.total_value)
    
    return AdminStatsResponse(
        total_users=total_users,
        active_users=active_users,
        total_bots=total_bots,
        running_bots=running_bots,
        total_trades_24h=total_trades_24h,
        successful_trades_24h=successful_trades_24h,
        total_volume_24h=total_volume_24h,
        system_uptime="24h 30m"  # Simplified - would track actual uptime
    )


@router.get("/users", response_model=List[AdminUserResponse])
async def get_all_users(
    admin_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db),
    limit: int = Query(50, ge=1, le=1000),
    offset: int = Query(0, ge=0),
    role: Optional[UserRole] = Query(None),
    status: Optional[UserStatus] = Query(None),
    search: Optional[str] = Query(None)
):
    """Get all users with filtering"""
    
    query = db.query(User)
    
    # Apply filters
    if role:
        query = query.filter(User.role == role)
    
    if status:
        query = query.filter(User.status == status)
    
    if search:
        search_term = f"%{search}%"
        query = query.filter(
            (User.email.ilike(search_term)) |
            (User.username.ilike(search_term)) |
            (User.first_name.ilike(search_term)) |
            (User.last_name.ilike(search_term))
        )
    
    users = query.order_by(User.created_at.desc()).offset(offset).limit(limit).all()
    
    # Get bot and trade counts for each user
    user_ids = [user.id for user in users]
    
    bot_counts = dict(
        db.query(TradingBot.user_id, func.count(TradingBot.id))
        .filter(TradingBot.user_id.in_(user_ids))
        .group_by(TradingBot.user_id)
        .all()
    )
    
    trade_counts = dict(
        db.query(Trade.user_id, func.count(Trade.id))
        .filter(Trade.user_id.in_(user_ids))
        .group_by(Trade.user_id)
        .all()
    )
    
    return [
        AdminUserResponse(
            id=user.id,
            email=user.email,
            username=user.username,
            first_name=user.first_name,
            last_name=user.last_name,
            role=user.role.value,
            status=user.status.value,
            is_2fa_enabled=user.is_2fa_enabled,
            is_email_verified=user.is_email_verified,
            max_bots=user.max_bots,
            max_concurrent_trades=user.max_concurrent_trades,
            risk_limit_percentage=user.risk_limit_percentage,
            created_at=user.created_at.isoformat(),
            last_login=user.last_login.isoformat() if user.last_login else None,
            bot_count=bot_counts.get(user.id, 0),
            trade_count=trade_counts.get(user.id, 0)
        )
        for user in users
    ]


@router.get("/users/{user_id}", response_model=AdminUserResponse)
async def get_user_details(
    user_id: int,
    admin_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """Get detailed user information"""
    
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    # Get counts
    bot_count = db.query(TradingBot).filter(TradingBot.user_id == user_id).count()
    trade_count = db.query(Trade).filter(Trade.user_id == user_id).count()
    
    return AdminUserResponse(
        id=user.id,
        email=user.email,
        username=user.username,
        first_name=user.first_name,
        last_name=user.last_name,
        role=user.role.value,
        status=user.status.value,
        is_2fa_enabled=user.is_2fa_enabled,
        is_email_verified=user.is_email_verified,
        max_bots=user.max_bots,
        max_concurrent_trades=user.max_concurrent_trades,
        risk_limit_percentage=user.risk_limit_percentage,
        created_at=user.created_at.isoformat(),
        last_login=user.last_login.isoformat() if user.last_login else None,
        bot_count=bot_count,
        trade_count=trade_count
    )


@router.put("/users/{user_id}")
async def update_user(
    user_id: int,
    request: UserUpdateRequest,
    admin_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """Update user settings"""
    
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    # Update fields
    update_data = request.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(user, field, value)
    
    db.commit()
    
    # Log admin action
    await AuditService.log_action(
        user_id=admin_user.id,
        action="admin_user_update",
        resource=f"user:{user_id}",
        details=update_data,
        success=True
    )
    
    return {"message": "User updated successfully"}


@router.post("/users/{user_id}/reset-hwid")
async def reset_user_hwid(
    user_id: int,
    admin_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """Reset user's HWID"""
    
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    user.hwid = None
    db.commit()
    
    # Log admin action
    await AuditService.log_action(
        user_id=admin_user.id,
        action="admin_hwid_reset",
        resource=f"user:{user_id}",
        success=True
    )
    
    return {"message": "User HWID reset successfully"}


@router.post("/users/{user_id}/disable-2fa")
async def disable_user_2fa(
    user_id: int,
    admin_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """Disable 2FA for user"""
    
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    user.is_2fa_enabled = False
    user.totp_secret = None
    db.commit()
    
    # Log admin action
    await AuditService.log_action(
        user_id=admin_user.id,
        action="admin_2fa_disable",
        resource=f"user:{user_id}",
        success=True
    )
    
    return {"message": "2FA disabled for user"}


@router.get("/bots")
async def get_all_bots(
    admin_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db),
    limit: int = Query(50, ge=1, le=1000),
    offset: int = Query(0, ge=0),
    status: Optional[BotStatus] = Query(None),
    user_id: Optional[int] = Query(None)
):
    """Get all bots across all users"""
    
    query = db.query(TradingBot).join(User)
    
    if status:
        query = query.filter(TradingBot.status == status)
    
    if user_id:
        query = query.filter(TradingBot.user_id == user_id)
    
    bots = query.order_by(TradingBot.created_at.desc()).offset(offset).limit(limit).all()
    
    return [
        {
            "id": bot.id,
            "name": bot.name,
            "user_id": bot.user_id,
            "user_email": bot.owner.email,
            "strategy": bot.strategy.value,
            "symbol": bot.symbol,
            "exchange": bot.exchange,
            "status": bot.status.value,
            "total_trades": bot.total_trades,
            "total_profit_loss": bot.total_profit_loss,
            "created_at": bot.created_at.isoformat(),
            "last_execution": bot.last_execution.isoformat() if bot.last_execution else None
        }
        for bot in bots
    ]


@router.post("/bots/{bot_id}/stop")
async def admin_stop_bot(
    bot_id: int,
    admin_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """Stop a bot (admin override)"""
    
    bot = db.query(TradingBot).filter(TradingBot.id == bot_id).first()
    if not bot:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Bot not found"
        )
    
    # Stop the bot
    from ..services.bot_engine import BotEngineService
    bot_engine = BotEngineService()
    success = await bot_engine.stop_bot(bot_id)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to stop bot"
        )
    
    # Log admin action
    await AuditService.log_action(
        user_id=admin_user.id,
        action="admin_bot_stop",
        resource=f"bot:{bot_id}",
        success=True
    )
    
    return {"message": "Bot stopped successfully"}


@router.get("/audit-logs")
async def get_audit_logs(
    admin_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db),
    limit: int = Query(100, ge=1, le=1000),
    offset: int = Query(0, ge=0),
    action: Optional[str] = Query(None),
    user_id: Optional[int] = Query(None),
    success: Optional[bool] = Query(None)
):
    """Get system audit logs"""
    
    query = db.query(AuditLog)
    
    if action:
        query = query.filter(AuditLog.action == action)
    
    if user_id:
        query = query.filter(AuditLog.user_id == user_id)
    
    if success is not None:
        query = query.filter(AuditLog.success == success)
    
    logs = query.order_by(AuditLog.created_at.desc()).offset(offset).limit(limit).all()
    
    return [
        {
            "id": log.id,
            "user_id": log.user_id,
            "action": log.action,
            "resource": log.resource,
            "details": log.details,
            "ip_address": log.ip_address,
            "success": log.success,
            "error_message": log.error_message,
            "created_at": log.created_at.isoformat()
        }
        for log in logs
    ]


@router.get("/system-health")
async def get_system_health(
    admin_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """Get system health status"""
    
    # Database health
    try:
        db.execute("SELECT 1")
        db_status = "healthy"
    except Exception:
        db_status = "unhealthy"
    
    # Redis health
    try:
        from ..core.database import get_redis
        redis_client = get_redis()
        redis_client.ping()
        redis_status = "healthy"
    except Exception:
        redis_status = "unhealthy"
    
    # Bot engine health
    from ..services.bot_engine import BotEngineService
    bot_engine = BotEngineService()
    running_bots = len(bot_engine.running_bots)
    
    # Recent errors
    recent_errors = db.query(AuditLog).filter(
        and_(
            AuditLog.success == False,
            AuditLog.created_at >= datetime.utcnow() - timedelta(hours=1)
        )
    ).count()
    
    return {
        "database": {"status": db_status},
        "redis": {"status": redis_status},
        "bot_engine": {
            "status": "healthy",
            "running_bots": running_bots
        },
        "recent_errors_1h": recent_errors,
        "overall_status": "healthy" if all([
            db_status == "healthy",
            redis_status == "healthy",
            recent_errors < 10
        ]) else "degraded"
    }


@router.post("/broadcast")
async def broadcast_message(
    title: str,
    message: str,
    admin_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """Broadcast message to all users"""
    
    from ..models.notifications import Notification, NotificationType, NotificationStatus
    
    # Get all active users
    users = db.query(User).filter(User.status == UserStatus.ACTIVE).all()
    
    # Create notifications for all users
    notifications = []
    for user in users:
        notification = Notification(
            user_id=user.id,
            title=title,
            message=message,
            notification_type=NotificationType.IN_APP,
            status=NotificationStatus.SENT
        )
        notifications.append(notification)
    
    db.add_all(notifications)
    db.commit()
    
    # Log admin action
    await AuditService.log_action(
        user_id=admin_user.id,
        action="admin_broadcast",
        details={"title": title, "recipients": len(users)},
        success=True
    )
    
    return {
        "message": "Broadcast sent successfully",
        "recipients": len(users)
    }
